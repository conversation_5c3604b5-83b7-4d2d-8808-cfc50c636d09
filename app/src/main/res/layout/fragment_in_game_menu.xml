<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true">

    <FrameLayout
        android:id="@+id/btn_menu"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginStart="@dimen/size_16"
        android:layout_marginBottom="@dimen/size_16"
        android:background="@drawable/bg_btn_ingame_menu"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <ImageView
            android:id="@+id/iv_menu"
            android:layout_width="19dp"
            android:layout_height="19dp"
            android:layout_gravity="center"
            android:src="@drawable/ic_ingame_menu_close" />
    </FrameLayout>

    <com.abrand.custom.ui.views.BonusWageringView
        android:id="@+id/bonus_wagering"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/container_talisman"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/bonus_wagering">

        <ImageView
            android:id="@+id/iv_talisman"
            android:layout_width="23dp"
            android:layout_height="28dp"
            android:layout_marginStart="20dp"
            android:scaleType="centerInside"
            app:layout_constraintBottom_toBottomOf="@+id/tv_talisman_name"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_talisman_title" />

        <TextView
            android:id="@+id/tv_talisman_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="16dp"
            android:alpha="0.6"
            android:text="@string/ingame_menu_talisman_title"
            android:textColor="#FFFFFF"
            android:textSize="12sp"
            app:layout_constraintStart_toEndOf="@+id/iv_talisman"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_talisman_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#55BEF9"
            android:textSize="14sp"
            app:layout_constraintStart_toStartOf="@+id/tv_talisman_title"
            app:layout_constraintTop_toBottomOf="@+id/tv_talisman_title" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/fragment_ingame_menu_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <TextView
        android:id="@+id/tv_search"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="80dp"
        android:layout_marginBottom="18dp"
        android:drawablePadding="24dp"
        android:text="@string/search"
        android:textColor="#FFFFFF"
        android:textSize="14sp"
        app:drawableStartCompat="@drawable/ic_search_white"
        app:layout_constraintBottom_toTopOf="@+id/center_messages"
        app:layout_constraintStart_toStartOf="parent" />

    <LinearLayout
        android:id="@+id/center_messages"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="80dp"
        android:layout_marginBottom="24dp"
        android:orientation="horizontal"
        app:layout_constraintBottom_toTopOf="@+id/tv_cash"
        app:layout_constraintStart_toStartOf="parent">

        <FrameLayout
            android:layout_width="26dp"
            android:layout_height="26dp"
            android:layout_marginStart="1dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginTop="4dp"
                android:layout_marginEnd="4dp"
                android:src="@drawable/ic_messages_default_white" />

            <FrameLayout
                android:id="@+id/center_messages_counter"
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:layout_gravity="top|end"
                android:background="@drawable/circle_messages_counter"
                android:visibility="invisible">

                <TextView
                    android:id="@+id/tv_center_messages_counter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:text="3"
                    android:textSize="8sp" />
            </FrameLayout>
        </FrameLayout>

        <TextView
            android:id="@+id/tv_messages"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="6dp"
            android:text="@string/messages"
            android:textColor="#FFFFFF"
            android:textSize="14sp" />
    </LinearLayout>

    <TextView
        android:id="@+id/tv_cash"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="80dp"
        android:layout_marginBottom="24dp"
        android:drawablePadding="24dp"
        android:text="@string/replenish_account"
        android:textColor="#FFFFFF"
        android:textSize="14sp"
        app:drawableStartCompat="@drawable/ic_ingame_cash"
        app:layout_constraintBottom_toTopOf="@+id/tv_bonuses"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/tv_bonuses"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="80dp"
        android:layout_marginBottom="24dp"
        android:drawablePadding="24dp"
        android:text="@string/bonuses"
        android:textColor="#FFFFFF"
        android:textSize="14sp"
        app:drawableStartCompat="@drawable/ic_bonuses_white"
        app:layout_constraintBottom_toTopOf="@+id/tv_tournament"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/tv_tournament"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="80dp"
        android:layout_marginBottom="24dp"
        android:drawablePadding="24dp"
        android:text="@string/tournament"
        android:textColor="#FFFFFF"
        android:textSize="14sp"
        app:drawableStartCompat="@drawable/ic_tournament_white"
        app:layout_constraintBottom_toTopOf="@+id/tv_support"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/tv_support"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="80dp"
        android:layout_marginBottom="24dp"
        android:drawablePadding="24dp"
        android:text="@string/support"
        android:textColor="#FFFFFF"
        android:textSize="14sp"
        app:drawableStartCompat="@drawable/ic_support"
        app:layout_constraintBottom_toTopOf="@+id/bottom_btn_barrier"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/bottom_btn_barrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="top"
        app:constraint_referenced_ids="btn_play_money,fast_click_payment_view" />

    <Button
        android:id="@+id/btn_play_money"
        style="@style/ButtonStyle"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_marginStart="80dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        android:text="@string/play_for_money"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <com.abrand.custom.ui.views.FastClickPaymentView
        android:id="@+id/fast_click_payment_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="80dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        app:layout_constraintBottom_toBottomOf="parent" />

    <ImageView
        android:id="@+id/iv_search"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginBottom="14dp"
        android:padding="8dp"
        android:src="@drawable/ic_search_green"
        app:layout_constraintBottom_toTopOf="@+id/side_messages"
        app:layout_constraintStart_toStartOf="parent" />

    <FrameLayout
        android:id="@+id/side_messages"
        android:layout_width="38dp"
        android:layout_height="38dp"
        android:layout_marginStart="24dp"
        android:layout_marginBottom="14dp"
        app:layout_constraintBottom_toTopOf="@+id/iv_cash"
        app:layout_constraintStart_toStartOf="parent">

        <ImageView
            android:id="@+id/iv_side_messages"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="8dp"
            android:layout_marginTop="6dp"
            android:layout_marginEnd="6dp"
            android:layout_marginBottom="8dp"
            android:src="@drawable/ic_messages_default_white" />

        <FrameLayout
            android:id="@+id/side_messages_counter"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:layout_gravity="top|end"
            android:layout_marginTop="2dp"
            android:layout_marginEnd="2dp"
            android:background="@drawable/circle_messages_counter"
            android:visibility="invisible">

            <TextView
                android:id="@+id/tv_side_messages_counter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="3"
                android:textSize="8sp" />
        </FrameLayout>
    </FrameLayout>

    <ImageView
        android:id="@+id/iv_cash"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginBottom="14dp"
        android:padding="8dp"
        android:src="@drawable/ic_ingame_cash"
        app:layout_constraintBottom_toTopOf="@+id/iv_bonuses"
        app:layout_constraintStart_toStartOf="parent" />

    <ImageView
        android:id="@+id/iv_bonuses"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginBottom="14dp"
        android:contentDescription="@string/ingame_side_menu_bonuses_description"
        android:padding="8dp"
        android:src="@drawable/ic_bonuses_white"
        app:layout_constraintBottom_toTopOf="@+id/iv_tournaments"
        app:layout_constraintStart_toStartOf="parent" />

    <ImageView
        android:id="@+id/iv_tournaments"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginBottom="14dp"
        android:padding="8dp"
        android:src="@drawable/ic_tournament_white"
        app:layout_constraintBottom_toTopOf="@+id/iv_support"
        app:layout_constraintStart_toStartOf="parent" />

    <ImageView
        android:id="@+id/iv_support"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginBottom="22dp"
        android:padding="8dp"
        android:src="@drawable/ic_support"
        app:layout_constraintBottom_toTopOf="@+id/requisite_view"
        app:layout_constraintStart_toStartOf="parent" />

    <com.abrand.custom.ui.views.RequisiteView
        android:id="@+id/requisite_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/size_16"
        android:layout_marginBottom="16dp"
        app:layout_constraintBottom_toTopOf="@+id/btn_menu"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
