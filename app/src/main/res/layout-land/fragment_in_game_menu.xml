<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true">

    <FrameLayout
        android:id="@+id/btn_menu"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginStart="48dp"
        android:layout_marginTop="@dimen/size_16"
        android:background="@drawable/bg_btn_ingame_menu"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_menu"
            android:layout_width="19dp"
            android:layout_height="19dp"
            android:layout_gravity="center"
            android:src="@drawable/ic_ingame_menu_close" />
    </FrameLayout>

    <com.abrand.custom.ui.views.BonusWageringView
        android:id="@+id/bonus_wagering"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="62dp"
        android:layout_marginEnd="48dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tv_cash"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/fragment_ingame_menu_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <TextView
        android:id="@+id/tv_search"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="64dp"
        android:layout_marginTop="24dp"
        android:drawablePadding="24dp"
        android:text="@string/search"
        android:textColor="#FFFFFF"
        android:textSize="14sp"
        app:drawableStartCompat="@drawable/ic_search_white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btn_menu" />

    <LinearLayout
        android:id="@+id/center_messages"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="64dp"
        android:layout_marginTop="18dp"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_search">

        <FrameLayout
            android:layout_width="26dp"
            android:layout_height="26dp"
            android:layout_marginStart="1dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginTop="4dp"
                android:layout_marginEnd="4dp"
                android:src="@drawable/ic_messages_default_white" />

            <FrameLayout
                android:id="@+id/center_messages_counter"
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:layout_gravity="top|end"
                android:background="@drawable/circle_messages_counter"
                android:visibility="invisible">

                <TextView
                    android:id="@+id/tv_center_messages_counter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:text="3"
                    android:textSize="8sp" />
            </FrameLayout>
        </FrameLayout>

        <TextView
            android:id="@+id/tv_messages"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="6dp"
            android:text="@string/messages"
            android:textColor="#FFFFFF"
            android:textSize="14sp" />
    </LinearLayout>

    <TextView
        android:id="@+id/tv_cash"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="64dp"
        android:layout_marginTop="24dp"
        android:drawablePadding="24dp"
        android:text="@string/replenish_account"
        android:textColor="#FFFFFF"
        android:textSize="14sp"
        app:drawableStartCompat="@drawable/ic_ingame_cash"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/center_messages" />

    <TextView
        android:id="@+id/tv_bonuses"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="64dp"
        android:layout_marginTop="24dp"
        android:drawablePadding="24dp"
        android:text="@string/bonuses"
        android:textColor="#FFFFFF"
        android:textSize="14sp"
        app:drawableStartCompat="@drawable/ic_bonuses_white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_cash" />

    <TextView
        android:id="@+id/tv_tournament"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="64dp"
        android:layout_marginTop="24dp"
        android:drawablePadding="24dp"
        android:text="@string/tournament"
        android:textColor="#FFFFFF"
        android:textSize="14sp"
        app:drawableStartCompat="@drawable/ic_tournament_white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_bonuses" />

    <TextView
        android:id="@+id/tv_support"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="64dp"
        android:layout_marginTop="24dp"
        android:drawablePadding="24dp"
        android:text="@string/support"
        android:textColor="#FFFFFF"
        android:textSize="14sp"
        app:drawableStartCompat="@drawable/ic_support"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_tournament" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/bottom_btn_barrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="top"
        app:constraint_referenced_ids="btn_play_money,fast_click_payment_view" />

    <Button
        android:id="@+id/btn_play_money"
        style="@style/ButtonStyle"
        android:layout_width="248dp"
        android:layout_height="48dp"
        android:layout_marginBottom="32dp"
        android:text="@string/play_for_money"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tv_cash" />

    <com.abrand.custom.ui.views.FastClickPaymentView
        android:id="@+id/fast_click_payment_view"
        android:layout_width="248dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tv_cash" />

    <ScrollView
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btn_menu">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingBottom="16dp">

            <ImageView
                android:id="@+id/iv_search"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="64dp"
                android:layout_marginTop="14dp"
                android:padding="8dp"
                android:src="@drawable/ic_search_green" />

            <FrameLayout
                android:id="@+id/side_messages"
                android:layout_width="38dp"
                android:layout_height="38dp"
                android:layout_marginStart="64dp"
                android:layout_marginTop="14dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/iv_search">

                <ImageView
                    android:id="@+id/iv_side_messages"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="8dp"
                    android:layout_marginTop="6dp"
                    android:layout_marginEnd="6dp"
                    android:layout_marginBottom="8dp"
                    android:src="@drawable/ic_messages_default_white" />

                <FrameLayout
                    android:id="@+id/side_messages_counter"
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:layout_gravity="top|end"
                    android:layout_marginTop="2dp"
                    android:layout_marginEnd="2dp"
                    android:background="@drawable/circle_messages_counter"
                    android:visibility="invisible">

                    <TextView
                        android:id="@+id/tv_side_messages_counter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:text="3"
                        android:textSize="8sp" />
                </FrameLayout>
            </FrameLayout>

            <ImageView
                android:id="@+id/iv_cash"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="64dp"
                android:layout_marginTop="14dp"
                android:padding="8dp"
                android:src="@drawable/ic_ingame_cash"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/side_messages" />

            <ImageView
                android:id="@+id/iv_bonuses"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="64dp"
                android:layout_marginTop="14dp"
                android:contentDescription="@string/ingame_side_menu_bonuses_description"
                android:padding="8dp"
                android:src="@drawable/ic_bonuses_white" />

            <ImageView
                android:id="@+id/iv_tournaments"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="64dp"
                android:layout_marginTop="14dp"
                android:padding="8dp"
                android:src="@drawable/ic_tournament_white"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/iv_bonuses" />

            <ImageView
                android:id="@+id/iv_support"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="64dp"
                android:layout_marginTop="14dp"
                android:padding="8dp"
                android:src="@drawable/ic_support"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/iv_tournaments" />

            <com.abrand.custom.ui.views.RequisiteView
                android:id="@+id/requisite_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="48dp"
                android:layout_marginTop="16dp"
                app:layout_constraintBottom_toTopOf="@+id/btn_menu"
                app:layout_constraintStart_toStartOf="parent" />
        </LinearLayout>
    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>
