package com.abrand.custom.data.entity

import com.google.gson.Gson
import com.google.gson.annotations.SerializedName

data class InstallConfig(
    val token: String,
    val domains: List<String>,
    @SerializedName("updater_domains")
    val updaterDomains: List<String>?,
    @SerializedName("download_code")
    val downloadCode: String,
    val uuid: String?,
    val appInstallUuid: String?,
    val ref: String?,
    val salt: String?,
    @SerializedName("affdata")
    val affData: Map<String, String>?,
)

fun InstallConfig.affdataToJson(): String {
    return Gson().toJson(this.affData)
}

fun InstallConfig.mapToLocalConfig(): LocalConfig {
    return LocalConfig(
        token = token,
        domains = domains,
        updaterDomains = updaterDomains,
        downloadCode = downloadCode,
        uuid = uuid,
        appInstallUuid = appInstallUuid,
        ref = ref,
        salt = salt,
        affData = affData,
    )
}
