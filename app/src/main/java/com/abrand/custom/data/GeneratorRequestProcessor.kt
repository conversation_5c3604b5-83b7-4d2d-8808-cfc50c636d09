package com.abrand.custom.data

import com.abrand.custom.data.entity.AllDomainsBannedException
import com.abrand.custom.data.entity.BannedDomainDTO
import com.abrand.custom.data.entity.GeneratorResponse
import com.abrand.custom.data.entity.GeneratorServerResponse
import com.abrand.custom.data.repositories.BannedDomainsRepositoryImpl
import com.abrand.custom.domain.BannedDomainsRepository
import com.abrand.custom.tools.Utils.validateUrl
import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import retrofit2.HttpException
import retrofit2.Response

class GeneratorRequestProcessor(
    private val bannedDomainsRepository: BannedDomainsRepository = BannedDomainsRepositoryImpl,
) {
    suspend fun processRequest(
        request: suspend (String) -> Response<GeneratorServerResponse>,
        domains: List<String>,
        tag: String
    ): GeneratorResponse {
        for (domain in domains) {
            try {
                val url = validateUrl(domain)
                val response = request(url)
                if (response.isSuccessful) {
                    val serverResponse = response.body()
                    if (serverResponse != null && serverResponse.status) {
                        return GeneratorResponse(
                            smenUrl = serverResponse.smenUrl,
                            data = serverResponse.data,
                            userAgentPostfix = serverResponse.userAgentPostfix
                        )
                    } else {
                        handleBannedDomains(
                            domain = domain,
                            reason = serverResponse?.message ?: UNKNOWN_MESSAGE_ERROR,
                            code = response.code(),
                            tag = tag,
                        )
                    }
                } else {
                    handleBannedDomains(
                        domain = domain,
                        reason = parseErrorReason(response),
                        code = response.code(),
                        tag = tag,
                    )
                }
            } catch (e: Exception) {
                handleBannedDomains(
                    domain = domain,
                    reason = e.toString(),
                    code = (e as? HttpException)?.code(),
                    tag = tag,
                )
            }
        }
        throw AllDomainsBannedException()
    }

    private suspend fun handleBannedDomains(
        domain: String,
        reason: String,
        code: Int?,
        tag: String
    ) {
        bannedDomainsRepository.saveBannedDomain(
            BannedDomainDTO(
                domain = domain,
                reason = reason,
                code = code,
                action = tag,
                time = System.currentTimeMillis()
            )
        )
    }

    private fun parseErrorReason(response: Response<GeneratorServerResponse>): String {
        return try {
            Gson().fromJson(
                response.errorBody()?.string(),
                GeneratorServerResponse::class.java
            ).message
        } catch (e: JsonSyntaxException) {
            UNKNOWN_MESSAGE_ERROR
        }
    }

    companion object {
        const val UNKNOWN_MESSAGE_ERROR = "Unknown error"
    }
}
