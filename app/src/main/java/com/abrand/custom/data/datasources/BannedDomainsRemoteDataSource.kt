package com.abrand.custom.data.datasources

import com.abrand.custom.data.entity.BannedDomainDTO
import com.abrand.custom.data.entity.BannedDomainResponseDTO
import com.abrand.custom.data.entity.LocalConfig

interface BannedDomainsRemoteDataSource {

    suspend fun sendBannedDomains(
        appConfig: LocalConfig,
        bannedDomainsList: List<BannedDomainDTO>,
        deviceId: String,
        country: String?
    ): BannedDomainResponseDTO
}
