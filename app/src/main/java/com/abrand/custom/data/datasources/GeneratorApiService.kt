package com.abrand.custom.data.datasources

import com.abrand.custom.BuildConfig
import com.abrand.custom.data.entity.GeneratorServerResponse
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Response
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.converter.scalars.ScalarsConverterFactory
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Headers
import retrofit2.http.POST
import retrofit2.http.Url
import java.util.concurrent.TimeUnit

interface GeneratorApiService {

    @POST
    @Headers("Accept: application/json", "X-identifyer: initCustomer")
    suspend fun initCustomer(
        @Url url: String,
        @Body analytics: String
    ): Response<GeneratorServerResponse>

    @GET
    @Headers("Accept: application/json", "X-identifyer: check")
    suspend fun check(
        @Url url: String
    ): Response<GeneratorServerResponse>

    @GET
    @Headers("Accept: application/json", "X-identifyer: getInstalled")
    suspend fun getInstalled(
        @Url url: String
    ): Response<GeneratorServerResponse>

    @GET
    suspend fun sendAnalytics(@Url url: String): String

    companion object {
        private const val OKHTTP_TIME_OUT = 30L

        fun provideService(okHttpClient: OkHttpClient = provideOkHttpClient()): GeneratorApiService =
            Retrofit.Builder()
                .baseUrl("https://example.com")
                .addConverterFactory(ScalarsConverterFactory.create())
                .addConverterFactory(GsonConverterFactory.create())
                .client(okHttpClient)
                .build()
                .create(GeneratorApiService::class.java)

        private fun provideOkHttpClient(isDebug: Boolean = BuildConfig.DEBUG): OkHttpClient {
            val builder = OkHttpClient.Builder()
                .connectTimeout(OKHTTP_TIME_OUT, TimeUnit.SECONDS)
                .readTimeout(OKHTTP_TIME_OUT, TimeUnit.SECONDS)
                .writeTimeout(OKHTTP_TIME_OUT, TimeUnit.SECONDS)

            if (isDebug) {
                val loggingInterceptor = HttpLoggingInterceptor().apply {
                    level = HttpLoggingInterceptor.Level.BODY
                }
                builder.addInterceptor(loggingInterceptor)
            }

            return builder.build()
        }
    }
}


