package com.abrand.custom.data

import android.content.Context
import android.content.SharedPreferences
import android.os.Build
import android.text.TextUtils
import androidx.core.content.edit
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKeys
import com.abrand.custom.BuildConfig
import com.abrand.custom.data.entity.AnalyticsEvent
import com.abrand.custom.data.entity.BiometricCredentials
import com.abrand.custom.data.entity.FastClickPaymentSystem
import com.abrand.custom.data.entity.LocalConfig
import com.abrand.custom.data.entity.LoyaltyStatus
import com.abrand.custom.data.entity.User
import com.abrand.custom.tools.Crypt
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import com.google.gson.reflect.TypeToken
import com.ironz.binaryprefs.BinaryPreferencesBuilder
import com.ironz.binaryprefs.encryption.AesValueEncryption
import com.ironz.binaryprefs.encryption.XorKeyEncryption

object Settings {
    private lateinit var preferences: SharedPreferences
    private val gson = Gson()

    @JvmStatic
    fun get(): Settings {
        return this
    }

    fun init(context: Context) {
        preferences = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            try {
                createJetpackPreferences(context)
            } catch (e: Exception) {
                e.printStackTrace()
                createYandexPreferences(context)
            }
        } else {
            createYandexPreferences(context)
        }
    }

    private fun createJetpackPreferences(context: Context): SharedPreferences {
        val masterKeyAlias = MasterKeys.getOrCreate(MasterKeys.AES256_GCM_SPEC)
        return EncryptedSharedPreferences.create(
            context.packageName,
            masterKeyAlias,
            context,
            EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
            EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
        )
    }

    private fun createYandexPreferences(context: Context): SharedPreferences {
        val encryptionBytes = getEncryptionBytes(context)
        val keyEncryption = XorKeyEncryption(encryptionBytes)
        val valueEncryption = AesValueEncryption(encryptionBytes, encryptionBytes)

        return BinaryPreferencesBuilder(context)
            .keyEncryption(keyEncryption)
            .valueEncryption(valueEncryption)
            .build()
    }

    private fun getEncryptionBytes(context: Context): ByteArray {
        val encryptionKeyLength = 16
        val packageNameBytes = context.packageName.toByteArray()
        var encryptionBytes = ByteArray(encryptionKeyLength)

        when {
            packageNameBytes.size > encryptionKeyLength -> {
                encryptionBytes = packageNameBytes.copyOfRange(0, encryptionKeyLength)
            }

            packageNameBytes.size < encryptionKeyLength -> {
                for (i in 0 until encryptionKeyLength) {
                    encryptionBytes[i] =
                        if (i < packageNameBytes.size) packageNameBytes[i] else packageNameBytes[0]
                }
            }

            else -> encryptionBytes = packageNameBytes
        }
        return encryptionBytes
    }

    var settingsVersion: Int
        get() = preferences.getInt(SETTINGS_VERSION, 0)
        set(value) = preferences.edit { putInt(SETTINGS_VERSION, value) }

    val userId: String
        get() = preferences.getString(USER_ID, "") ?: ""

    fun setUserId(value: String?) {
        preferences.edit {
            putString(USER_ID, value)
        }
    }

    var userName: String
        get() = preferences.getString(USER_NAME, "") ?: ""
        set(value) = preferences.edit { putString(USER_NAME, value) }

    val loggedUserEmail: String
        get() = preferences.getString(LOGGED_USER_EMAIL, "") ?: ""

    fun setLoggedUserEmail(value: String?) {
        preferences.edit {
            putString(LOGGED_USER_EMAIL, value)
        }
    }

    var pushToken: String
        get() = preferences.getString(PUSH_TOKEN, "") ?: ""
        set(value) = preferences.edit { putString(PUSH_TOKEN, value) }

    var isPushTokenMustBeUpdate: Boolean
        get() = preferences.getBoolean(PUSH_TOKEN_MUST_BE_UPDATE, false)
        set(value) = preferences.edit { putBoolean(PUSH_TOKEN_MUST_BE_UPDATE, value) }

    var userCurrencyCode: String
        get() = preferences.getString(USER_CURRENCY_CODE, "RUB") ?: "RUB"
        set(value) = preferences.edit {
            putString(USER_CURRENCY_CODE, value)
        }

    var userCurrencySymbol: String
        get() = preferences.getString(USER_CURRENCY_SYMBOL, "") ?: ""
        set(value) = preferences.edit { putString(USER_CURRENCY_SYMBOL, value) }

    var userOur: Int
        get() {
            return if (Crypt.decrypt(BuildConfig.DESTINATION) == BuildConfig.PRODUCT_DESTINATION) {
                1
            } else {
                preferences.getInt(USER_OUR, 1)
            }
        }
        set(value) = preferences.edit { putInt(USER_OUR, value) }

    var isFirstOpen: Boolean
        get() = preferences.getBoolean(FIRST_OPEN, true)
        set(value) = preferences.edit { putBoolean(FIRST_OPEN, value) }

    var newestImportanceUpdateLvl: Boolean
        get() = preferences.getBoolean(NEWEST_IMPORTANCE_UPDATE_LVL, true)
        set(value) = preferences.edit { putBoolean(NEWEST_IMPORTANCE_UPDATE_LVL, value) }

    var isGeneralNotificationEnable: Boolean
        get() = preferences.getBoolean(IS_GENERAL_NOTIFICATION_ENABLE, true)
        set(value) = preferences.edit { putBoolean(IS_GENERAL_NOTIFICATION_ENABLE, value) }

    var lastBiometricSuggestDateTime: Long
        get() = preferences.getLong(LAST_BIOMETRIC_SUGGEST_DATE_TIME, 0)
        set(value) = preferences.edit { putLong(LAST_BIOMETRIC_SUGGEST_DATE_TIME, value) }

    var isSocialAuth: Boolean
        get() = preferences.getBoolean(SOCIAL_AUTH, false)
        set(value) = preferences.edit { putBoolean(SOCIAL_AUTH, value) }

    var postponedAnalyticsEvents: ArrayList<AnalyticsEvent>?
        get() {
            val json = preferences.getString(POSTPONED_ANALYTICS_EVENTS, null)
            val type = object : TypeToken<ArrayList<AnalyticsEvent>>() {}.type
            return try {
                gson.fromJson(json, type)
            } catch (e: JsonSyntaxException) {
                FirebaseCrashlytics.getInstance().log("POSTPONED_ANALYTICS_EVENTS is: $json")
                FirebaseCrashlytics.getInstance().recordException(e)
                null
            }
        }
        set(value) {
            preferences.edit {
                val json = gson.toJson(value)
                putString(POSTPONED_ANALYTICS_EVENTS, json)
            }
        }

    var configAffData: Map<String, String>?
        get() = localConfig?.affData
        set(value) {
            val config = localConfig ?: return
            val newConfig = config.copy(
                affData = value ?: config.affData
            )
            localConfig = newConfig
        }

    var configAffDataV118: String?
        get() = preferences.getString(CONFIG_AFF_DATA, null)
        set(value) {
            preferences.edit {
                putString(CONFIG_AFF_DATA, value)
            }
        }

    var localConfig: LocalConfig?
        get() {
            val json = preferences.getString(LOCAL_CONFIG, null)
            return try {
                gson.fromJson(json, LocalConfig::class.java)
            } catch (e: JsonSyntaxException) {
                FirebaseCrashlytics.getInstance().log("$LOCAL_CONFIG is: $json")
                FirebaseCrashlytics.getInstance().recordException(e)
                null
            }
        }
        set(value) {
            preferences.edit {
                val json = gson.toJson(value)
                putString(LOCAL_CONFIG, json)
            }
        }

    var biometricCredentials: BiometricCredentials?
        get() {
            val json = preferences.getString(BIOMETRIC_CREDENTIALS, null)
            val type = object : TypeToken<BiometricCredentials>() {}.type
            return try {
                gson.fromJson(json, type)
            } catch (e: JsonSyntaxException) {
                FirebaseCrashlytics.getInstance().log("BIOMETRIC_CREDENTIALS is: $json")
                FirebaseCrashlytics.getInstance().recordException(e)
                null
            }
        }
        set(value) {
            preferences.edit {
                putString(BIOMETRIC_CREDENTIALS, gson.toJson(value))
            }
        }

    val userState: User.State
        get() = when {
            userOur == 0 -> User.State.ORGANIC
            TextUtils.isEmpty(userId) -> User.State.NOT_LOGGED
            else -> User.State.PLAYER
        }

    var refCode: String
        get() = localConfig?.ref ?: ""
        set(value) {
            val config = localConfig ?: return
            localConfig = config.copy(ref = value)
        }

    var refCodeV118: String
        get() = preferences.getString(REF_CODE, "") ?: ""
        set(value) {
            preferences.edit { putString(REF_CODE, value) }
        }

    var fastClickPaymentSystem: FastClickPaymentSystem?
        get() {
            val json = preferences.getString(FAST_CLICK_PAYMENT_SYSTEM, null)
            val type = object : TypeToken<FastClickPaymentSystem>() {}.type
            return try {
                gson.fromJson(json, type)
            } catch (e: JsonSyntaxException) {
                FirebaseCrashlytics.getInstance().log("FAST_CLICK_PAYMENT_SYSTEM is: $json")
                FirebaseCrashlytics.getInstance().recordException(e)
                null
            }
        }
        set(value) {
            preferences.edit {
                putString(FAST_CLICK_PAYMENT_SYSTEM, gson.toJson(value))
            }
        }

    var currentLoyaltyStatus: LoyaltyStatus?
        get() {
            val json = preferences.getString(LOYALTY_STATUS, null)
            val type = object : TypeToken<LoyaltyStatus>() {}.type
            return try {
                gson.fromJson(json, type)
            } catch (e: Exception) {
                FirebaseCrashlytics.getInstance().log("LOYALTY_STATUS is: $json")
                FirebaseCrashlytics.getInstance().recordException(e)
                null
            }
        }
        set(value) {
            preferences.edit {
                putString(LOYALTY_STATUS, gson.toJson(value))
            }
        }

    var postponedLoyaltyStatus: LoyaltyStatus?
        get() {
            val json = preferences.getString(POSTPONED_LOYALTY_STATUS, null)
            val type = object : TypeToken<LoyaltyStatus>() {}.type
            return try {
                gson.fromJson(json, type)
            } catch (e: Exception) {
                FirebaseCrashlytics.getInstance().log("POSTPONED_LOYALTY_STATUS is: $json")
                FirebaseCrashlytics.getInstance().recordException(e)
                null
            }
        }
        set(value) {
            preferences.edit {
                putString(POSTPONED_LOYALTY_STATUS, gson.toJson(value))
            }
        }

    private const val SETTINGS_VERSION = "settings_version"
    private const val USER_ID = "ui"
    private const val USER_NAME = "un"
    private const val IS_TERMS_ACCEPTED = "ita"
    private const val USER_OUR = "uo"
    private const val LOGGED_USER_EMAIL = "lue"
    private const val PUSH_TOKEN = "push_token"
    private const val USER_CURRENCY_CODE = "user_currency_code"
    private const val USER_CURRENCY_SYMBOL = "user_currency_symbol"
    private const val NEWEST_IMPORTANCE_UPDATE_LVL = "niul"
    private const val FIRST_OPEN = "first_open"
    private const val IS_GENERAL_NOTIFICATION_ENABLE = "isGeneralNotificationEnable"
    private const val POSTPONED_ANALYTICS_EVENTS = "postponedAnalyticsEvents"
    private const val REF_CODE = "refCode"
    private const val PUSH_TOKEN_MUST_BE_UPDATE = "pushTokenMustBeUpdate"
    private const val CONFIG_AFF_DATA = "configAffData"
    private const val LOCAL_CONFIG = "localConfig"
    private const val BIOMETRIC_CREDENTIALS = "biometricCredentials"
    private const val LAST_BIOMETRIC_SUGGEST_DATE_TIME = "lastBiometricSuggestDateTime"
    private const val FAST_CLICK_PAYMENT_SYSTEM = "fastClickPaymentSystem"
    private const val SOCIAL_AUTH = "socialAuth"
    private const val LOYALTY_STATUS = "loyaltyStatus"
    private const val POSTPONED_LOYALTY_STATUS = "postponedLoyaltyStatus"
}
