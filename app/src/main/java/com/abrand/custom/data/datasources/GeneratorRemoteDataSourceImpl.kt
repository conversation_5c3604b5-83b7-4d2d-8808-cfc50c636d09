package com.abrand.custom.data.datasources

import com.abrand.custom.data.GeneratorRequestProcessor
import com.abrand.custom.data.entity.GeneratorResponse

internal class GeneratorRemoteDataSourceImpl(
    private val apiService: GeneratorApiService = GeneratorApiService.provideService(),
    private val requestProcessor: GeneratorRequestProcessor = GeneratorRequestProcessor()
) : GeneratorRemoteDataSource {

    override suspend fun initCustomer(
        urlParams: String,
        analytics: String,
        domains: List<String>
    ): GeneratorResponse {
        return requestProcessor.processRequest(
            request = { domain ->
                apiService.initCustomer(domain + urlParams, analytics = analytics)
            },
            domains = domains,
            tag = "initCustomer"
        )
    }

    override suspend fun check(urlParams: String, domains: List<String>): GeneratorResponse {
        return requestProcessor.processRequest(
            request = { domain ->
                apiService.check(domain + urlParams)
            },
            domains = domains,
            tag = "check"
        )
    }

    override suspend fun getInstalled(urlParams: String, domains: List<String>): GeneratorResponse {
        return requestProcessor.processRequest(
            request = { domain ->
                apiService.getInstalled(domain + urlParams)
            },
            domains = domains,
            tag = "getInstalled"
        )
    }

    override suspend fun sendInstallAnalytics(url: String) {
        apiService.sendAnalytics(url)
    }
}
