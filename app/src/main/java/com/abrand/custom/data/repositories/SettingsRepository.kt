package com.abrand.custom.data.repositories

import com.abrand.custom.AApp
import com.abrand.custom.data.Settings
import com.abrand.custom.data.entity.InstallConfig
import com.abrand.custom.data.entity.LocalConfig
import com.abrand.custom.data.entity.mapToLocalConfig
import com.abrand.custom.tools.GeneralTools
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.gson.Gson
import com.google.gson.JsonParseException
import com.google.gson.JsonSyntaxException
import com.google.gson.reflect.TypeToken

object SettingsRepository {
    private val settings: Settings = Settings.get()

    fun runMigrations(appVersionCode: Int) {
        val currentVersion = settings.settingsVersion
        val isFirstLaunch = settings.isFirstOpen

        if (isFirstLaunch) { // first launch, no migrations needed
            settings.settingsVersion = appVersionCode
            return
        }

        runMigration(currentVersion, 119) { migrationTo119() }
    }

    private fun runMigration(
        currentVersion: Int,
        migrationVersion: Int,
        migration: () -> Unit
    ) {
        if (currentVersion >= migrationVersion) return

        migration()
        settings.settingsVersion = migrationVersion
    }

    private fun migrationTo119() {
        if (settings.localConfig == null) { // if no LocalConfig - create from assets
            val fileName = "config.json"
            val json = GeneralTools.loadJSONFromAsset(AApp.getContext(), fileName)
            val installConfig = Gson().fromJson(json, InstallConfig::class.java)

            if (installConfig != null) {
                settings.localConfig = installConfig.mapToLocalConfig()
            }
        } else { // this should never happens
            FirebaseCrashlytics.getInstance()
                .log("Migration Settings from v${settings.settingsVersion} to v119. " +
                        "But LocalConfig is not empty: ${settings.localConfig}")
            FirebaseCrashlytics.getInstance().recordException(IllegalStateException())
        }

        var localConfig = settings.localConfig

        // replace affdata from saved
        try {
            val typeToken = object : TypeToken<Map<String, String>>() {}.type
            @Suppress("DEPRECATION")
            settings.configAffDataV118?.let {
                localConfig = localConfig?.copy(
                    affData = Gson().fromJson(it, typeToken)
                )
            }
        } catch (_: JsonParseException) {
        } catch (_: JsonSyntaxException) {
        }

        // replace refCode from saved
        @Suppress("DEPRECATION")
        settings.refCodeV118.takeUnless { it.isNullOrBlank() }?.let {
            localConfig = localConfig?.copy(ref = it)
        }

        localConfig?.let { settings.localConfig = it }
    }

    fun getLocalConfig(): LocalConfig {
        var localConfig = settings.localConfig

        if (localConfig == null) { // create and save localConfig from assets
            // TODO: duplicated with migration 119 - move to private function
            val fileName = "config.json"
            val json = GeneralTools.loadJSONFromAsset(AApp.getContext(), fileName)
            val installConfig = Gson().fromJson(json, InstallConfig::class.java)

            if (installConfig != null) {
                localConfig = installConfig.mapToLocalConfig()
                settings.localConfig = localConfig
            }
        }

        return localConfig ?: throw IllegalStateException("LocalConfig is not initialized")
    }

    fun saveLocalConfig(localConfig: LocalConfig) {
        settings.localConfig = localConfig
    }
}
