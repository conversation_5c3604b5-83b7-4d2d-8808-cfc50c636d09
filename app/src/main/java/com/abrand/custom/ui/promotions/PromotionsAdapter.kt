package com.abrand.custom.ui.promotions

import android.content.Context
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.os.CountDownTimer
import android.text.TextUtils
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.Button
import android.widget.EditText
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.RoundedBitmapDrawableFactory
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.abrand.custom.R
import com.abrand.custom.data.ApoloConfig
import com.abrand.custom.data.entity.FastClickPaymentSystem
import com.abrand.custom.data.entity.PromotionItem
import com.abrand.custom.data.repositories.YearRepository
import com.abrand.custom.databinding.ItemPermanentPromotionBinding
import com.abrand.custom.interfaces.FooterListener
import com.abrand.custom.presenter.PromotionItemDiffCallback
import com.abrand.custom.tools.DateTools
import com.abrand.custom.tools.GeneralTools
import com.abrand.custom.tools.setHtmlText
import com.abrand.custom.ui.views.FastClickPaymentView
import com.abrand.custom.ui.views.PromoCodeField
import com.squareup.picasso.Picasso
import com.squareup.picasso.Picasso.LoadedFrom
import com.squareup.picasso.Target
import java.text.ParseException
import java.util.Calendar
import java.util.Date
import java.util.TimeZone

class PromotionsAdapter() : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    var items = mutableListOf<PromotionItem>()
    var buttonsClickListener: ButtonsClickListener? = null
    var footerListener: FooterListener? = null
    var fastClickPaymentSystem: FastClickPaymentSystem? = null
    lateinit var currencyCode: String
    var bonusFastClickPaymentHolder: PromotionVH? = null
    var timers = mutableMapOf<Int, CountDownTimer>()
    private val hoursToStartTimer = 48

    companion object {
        const val TAG = "PromotionsAdapter";
    }

    constructor(currencyCode: String, fastClickPaymentSystem: FastClickPaymentSystem?) : this() {
        this.currencyCode = currencyCode
        this.fastClickPaymentSystem = fastClickPaymentSystem
        items.add(PromotionItem(PromotionItem.ItemType.FOOTER))
    }

    fun setList(newList: MutableList<PromotionItem>) {
        for ((_, timer) in timers) {
            timer.cancel()
        }
        timers.clear()

        newList.add(PromotionItem(PromotionItem.ItemType.FOOTER))
        val diffResult = DiffUtil.calculateDiff(PromotionItemDiffCallback(items, newList))
        this.items = newList
        diffResult.dispatchUpdatesTo(this)
    }

    fun updateFastClickPaymentSystem(fastClickPaymentSystem: FastClickPaymentSystem) {
        this.fastClickPaymentSystem = fastClickPaymentSystem
    }

    fun fastClickPaymentViewShowLoader(bonusId: Int) {
        bonusFastClickPaymentHolder?.fastClickPaymentViewShowLoader()
    }

    fun fastClickPaymentViewShowSuccessfulRebillingMessage() {
        bonusFastClickPaymentHolder?.fastClickPaymentViewShowSuccessfulRebillingMessage()
        bonusFastClickPaymentHolder = null
    }

    fun fastClickPaymentViewHideLoader() {
        bonusFastClickPaymentHolder?.fastClickPaymentViewHideLoader()
        bonusFastClickPaymentHolder = null
    }

    fun getBonusEvent(bonusId: Int): PromotionItem.BonusEvent? {
        for (item in items) {
            if (item.id == bonusId) {
                return item.bonus?.bonusEvent
            }
        }
        return null
    }

    fun getActiveProgressiveCurrentStep(): Int {
        for (item in items) {
            if ((item.bonus?.isActivated == true) &&
                    (item.bonus?.isProgressive == true) &&
                    item.bonus?.progressiveCurrentStep != null) {
                return item.bonus?.progressiveCurrentStep!!
            }
        }
        return -1
    }

    fun getAdapterPosition(promotionId: Int): Int {
        return items.indexOfFirst {
            it.id == promotionId
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        return when (viewType) {
            PromotionItem.ItemType.TITLE.id -> TitleVH(inflater, parent)
            PromotionItem.ItemType.NO_ACTIVE_BONUSES.id -> NoActiveBonusesVH(inflater, parent)
            PromotionItem.ItemType.BONUS.id -> PromotionVH(inflater, parent)
            PromotionItem.ItemType.PERMANENT_PROMOTION.id -> {
                val view = ItemPermanentPromotionBinding.inflate(inflater, parent, false)
                PermanentPromotionVH(view)
            }
            else -> FooterVH(inflater, parent)
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val context = holder.itemView.context
        val promotion: PromotionItem = items[position]
        if (holder is TitleVH) {
            holder.tvTitle.text = promotion.title
        } else if (holder is PromotionVH) {
            holder.bind(promotion, context, currencyCode)
        } else if (holder is PermanentPromotionVH) {
            holder.bind(holder.adapterPosition, promotion)
        } else if (holder is FooterVH) {
            holder.bind()
        }

        setFirstItemAppBarPadding(position, holder)
    }

    private fun setFirstItemAppBarPadding(position: Int, holder: RecyclerView.ViewHolder) {
        if (position == 0) {
            val padding: Int = GeneralTools.getActionBarHeight(holder.itemView.context)
            holder.itemView.setPadding(
                    holder.itemView.paddingLeft,
                    padding,
                    holder.itemView.paddingRight,
                    holder.itemView.paddingBottom
            )
        } else {
            holder.itemView.setPadding(
                    holder.itemView.paddingLeft,
                    0,
                    holder.itemView.paddingRight,
                    holder.itemView.paddingBottom
            )
        }
    }

    override fun getItemCount() = items.size

    override fun getItemViewType(position: Int): Int {
        return items[position].itemType.id
    }

    class TitleVH(inflater: LayoutInflater, parent: ViewGroup) :
            RecyclerView.ViewHolder(inflater.inflate(R.layout.item_promotion_title, parent, false)) {
        var tvTitle: TextView = itemView.findViewById(R.id.tv_title)
    }

    class NoActiveBonusesVH(inflater: LayoutInflater, parent: ViewGroup) :
            RecyclerView.ViewHolder(inflater.inflate(R.layout.item_no_active_bonuses, parent, false))

    inner class PromotionVH(inflater: LayoutInflater, parent: ViewGroup) :
            RecyclerView.ViewHolder(inflater.inflate(R.layout.item_promotion, parent, false)) {
        private var cardRoot: ConstraintLayout = itemView.findViewById(R.id.card_root)
        private var tvName: TextView = itemView.findViewById(R.id.tv_name)
        private var tvProgress: TextView = itemView.findViewById(R.id.tv_progress)
        private var progress: ProgressBar = itemView.findViewById(R.id.progress)
        private val tvDeposit: TextView = itemView.findViewById(R.id.tv_deposit)
        private val tvDepositTitle: TextView = itemView.findViewById(R.id.tv_deposit_title)
        private val tvDateEnd: TextView = itemView.findViewById(R.id.tv_date_end)
        private val tvDateEndTitle: TextView = itemView.findViewById(R.id.tv_date_end_title)
        private val tvConfirmTitle: TextView = itemView.findViewById(R.id.tv_confirm_title)
        private val tvToEndPromotionTitle: TextView = itemView.findViewById(R.id.tv_to_end_promotion_title)
        private val tvToEndPromotion: TextView = itemView.findViewById(R.id.tv_to_end_promotion)
        private val ivPromotion: ImageView = itemView.findViewById(R.id.iv_promotion)
        private val btnAction: Button = itemView.findViewById(R.id.btn_action)
        private val containerPromoCode: RelativeLayout = itemView.findViewById(R.id.container_promo_code)
        private val etPromoCode: EditText = itemView.findViewById(R.id.et_promo_code)
        private val ibPromoCodeEnter: ImageButton = itemView.findViewById(R.id.ib_promo_code_enter)
        private val ibInfo: ImageButton = itemView.findViewById(R.id.ib_promotion_info)
        private val ibDeactivate: ImageButton = itemView.findViewById(R.id.ib_promotion_deactivate)
        private var fastClickPaymentView: FastClickPaymentView = itemView.findViewById(R.id.fast_click_payment)
        private var timer: CountDownTimer? = null
        private var promotionItem: PromotionItem? = null

        private val promotionBackgroundTarget: Target = object : Target {
            override fun onBitmapLoaded(bitmap: Bitmap, from: LoadedFrom) {
                val roundedBitmapDrawable = RoundedBitmapDrawableFactory.create(parent.context.resources, bitmap)
                roundedBitmapDrawable.cornerRadius = parent.context.resources.getDimensionPixelSize(R.dimen.promotion_card_corner_radius).toFloat()
                cardRoot.background = roundedBitmapDrawable
            }

            override fun onBitmapFailed(e: java.lang.Exception, errorDrawable: Drawable) {
                cardRoot.background = ContextCompat.getDrawable(parent.context, R.drawable.promotion_card_default_bg)
            }

            override fun onPrepareLoad(placeHolderDrawable: Drawable?) {}
        }

        private val promotionImageCharacterTarget: Target = object : Target {
            override fun onBitmapLoaded(bitmap: Bitmap, from: LoadedFrom) {
                bitmap.let {
                    ivPromotion.scaleType = ImageView.ScaleType.CENTER_INSIDE
                    ivPromotion.adjustViewBounds = true
                    ivPromotion.setImageBitmap(it)
                }
            }

            override fun onBitmapFailed(e: java.lang.Exception, errorDrawable: Drawable) {}

            override fun onPrepareLoad(placeHolderDrawable: Drawable?) {}
        }

        fun bind(promotion: PromotionItem, context: Context, currencyCode: String) {
            promotionItem = promotion
            setPromotionCardHeight(promotion)
            setTitle(promotion)
            tvDeposit.text = GeneralTools.formatBalance(currencyCode, promotion.bonus?.depositAmount)
            setBonusBackground(promotion.bonus?.imgPattern, context, cardRoot)
            setBonusImageCharacter(promotion.bonus?.imgCharacter, ivPromotion)

            if (promotion.bonus?.isActivated == true) {
                initActivatedPromotion(promotion, context)
            } else {
                initNotActivatedPromotion(promotion)
            }

            if (PromotionItem.BonusEvent.EMAIL_CONFIRMATION == promotion.bonus?.bonusEvent ||
                    PromotionItem.BonusEvent.PHONE_CONFIRMATION == promotion.bonus?.bonusEvent) {
                tvDeposit.visibility = View.GONE
                tvDepositTitle.visibility = View.GONE
                tvDateEnd.visibility = View.GONE
                tvDateEndTitle.visibility = View.GONE
                tvConfirmTitle.visibility = View.VISIBLE
                tvToEndPromotionTitle.visibility = View.VISIBLE
                tvToEndPromotion.visibility = View.VISIBLE

                if (PromotionItem.BonusEvent.EMAIL_CONFIRMATION == promotion.bonus?.bonusEvent) {
                    tvConfirmTitle.setText(R.string.for_email_confirmation)
                } else if (PromotionItem.BonusEvent.PHONE_CONFIRMATION == promotion.bonus?.bonusEvent) {
                    tvConfirmTitle.setText(R.string.for_phone_confirmation)
                }
                val millisToEnd = promotion.bonus?.secondsToEnd?.times(DateTools.MILLIS_IN_SECOND) ?: 0
                val days = (millisToEnd / DateTools.MILLIS_IN_DAY) + 1
                tvToEndPromotion.text = "$days " + getDayAddition(days)
            } else {
                tvDeposit.visibility = View.VISIBLE
                tvDepositTitle.visibility = View.VISIBLE
                tvDateEnd.visibility = View.VISIBLE
                tvDateEndTitle.visibility = View.VISIBLE
                tvConfirmTitle.visibility = View.GONE
                tvToEndPromotionTitle.visibility = View.GONE
                tvToEndPromotion.visibility = View.GONE

                promotion.id?.let { it ->
                    val millisToEnd = getMillisDiff(context, promotion.bonus?.dateEnd.toString())
                    val hoursToEnd = millisToEnd / DateTools.MILLIS_IN_HOUR
                    if (hoursToEnd >= hoursToStartTimer) {
                        val days = (millisToEnd / DateTools.MILLIS_IN_DAY)
                        tvDateEnd.text = "$days " + getDayAddition(days.toInt())
                    } else if ((promotion.bonus?.isActivated != null && promotion.bonus?.isActivated!!) ||
                            (promotion.bonus?.isDeactivated != null && promotion.bonus?.isDeactivated!!)) {
                        startTimer(context, millisToEnd, this@PromotionVH)
                    } else {
                        timers[it]?.cancel()
                        tvDateEnd.text = DateTools.formatTime(promotion.bonus?.secondsToEnd?.times(DateTools.MILLIS_IN_SECOND.toLong())
                                ?: 0)
                    }
                }
            }

            ibInfo.setOnClickListener {
                buttonsClickListener?.onInfoButtonClicked(promotion)
            }
            ibDeactivate.setOnClickListener {
                promotion.id?.let { bonusId ->
                    promotion.bonus?.name?.let { bonusName ->
                        promotion.bonus?.availableAfterDeactivation?.let { availableAfterDeactivation ->
                            buttonsClickListener?.onDeactivatedButtonClicked(bonusId, bonusName, availableAfterDeactivation)
                        }
                    }
                }
            }
            btnAction.setOnClickListener {
                val isEmailOrPhoneBonus = PromotionItem.BonusEvent.EMAIL_CONFIRMATION == promotion.bonus?.bonusEvent ||
                        PromotionItem.BonusEvent.PHONE_CONFIRMATION == promotion.bonus?.bonusEvent
                buttonsClickListener?.onMainButtonClicked(promotion.id, promotion.bonus?.isActivated, isEmailOrPhoneBonus)
            }
        }

        private fun setBonusBackground(imgPattern: String?, context: Context, cardRoot: View) {
            if (!TextUtils.isEmpty(imgPattern)) {
                Picasso.get().load(ApoloConfig.getFullUrl(imgPattern))
                    .into(promotionBackgroundTarget)
            } else {
                cardRoot.background = ContextCompat.getDrawable(context, R.drawable.promotion_card_default_bg)
            }
        }

        private fun setBonusImageCharacter(imgCharacter: String?, ivPromotion: ImageView) {
            if (!TextUtils.isEmpty(imgCharacter)) {
                Picasso.get().load(ApoloConfig.getFullUrl(imgCharacter))
                    .into(promotionImageCharacterTarget)
            } else {
                ivPromotion.setImageBitmap(null)
            }
        }

        private fun setPromotionCardHeight(promotion: PromotionItem) {
            val params = cardRoot.layoutParams
            if (promotion.bonus?.isActivated == true && isProgressBonus(promotion)) {
                params.height = itemView.context.resources.getDimensionPixelSize(R.dimen.promotion_card_progress_height)
            } else {
                params.height = itemView.context.resources.getDimensionPixelSize(R.dimen.promotion_card_defautl_height)
            }
            cardRoot.layoutParams = params
        }

        private fun setTitle(promotion: PromotionItem) {
            var promotionName = promotion.bonus?.name
            promotionName = promotionName?.replace("</br>", "<br>")
            promotionName?.apply {
                tvName.setHtmlText(this)
            }
        }

        private fun initNotActivatedPromotion(promotion: PromotionItem) {
            ibDeactivate.visibility = View.GONE
            fastClickPaymentView.visibility = View.GONE
            tvProgress.visibility = View.GONE
            progress.visibility = View.GONE
            btnAction.text = promotion.bonus?.buttonName

            if (promotion.bonus?.isPromoCodeRequired == true) {
                btnAction.visibility = View.GONE
                containerPromoCode.visibility = View.VISIBLE
                etPromoCode.setOnEditorActionListener { v, actionId, event ->
                    if (actionId == EditorInfo.IME_ACTION_DONE) {
                        buttonsClickListener?.onPromoCodeEntered(promotion.id!!, etPromoCode.text.toString())
                    }
                    false
                }
                ibPromoCodeEnter.setOnClickListener {
                    if (promotion.id != null) {
                        buttonsClickListener?.onPromoCodeEntered(promotion.id!!, etPromoCode.text.toString())
                    }
                }
            } else {
                btnAction.visibility = View.VISIBLE
                containerPromoCode.visibility = View.GONE
            }
        }

        private fun initActivatedPromotion(promotion: PromotionItem, context: Context) {
            containerPromoCode.visibility = View.GONE
            ibDeactivate.visibility = View.VISIBLE

            val depositAmount = promotion.bonus?.depositAmount
            val paidSumOfDeposits = promotion.bonus?.paidSumOfDeposits
            if (depositAmount != null && 0.0 != depositAmount && paidSumOfDeposits != null) {
                tvProgress.visibility = View.VISIBLE
                progress.visibility = View.VISIBLE

                val leftSum = depositAmount - paidSumOfDeposits
                if (leftSum > 0) {
                    val leftSumFormatted = GeneralTools.formatBalance(currencyCode, depositAmount - paidSumOfDeposits)
                    tvProgress.text = context.getString(R.string.bonus_remains_pay, leftSumFormatted)
                } else {
                    tvProgress.text = context.getString(R.string.bonus_conditions_met);
                }

                progress.progress = (paidSumOfDeposits.div(depositAmount) * 100).toInt()
                itemView.viewTreeObserver.addOnGlobalLayoutListener {
                    val newProgressParams = progress.layoutParams
                    newProgressParams.width = tvProgress.width
                    newProgressParams.height = tvProgress.height
                    progress.layoutParams = newProgressParams
                }
            } else {
                tvProgress.visibility = View.GONE
                progress.visibility = View.GONE
            }

            if (PromotionItem.BonusEvent.EMAIL_CONFIRMATION == promotion.bonus?.bonusEvent ||
                    PromotionItem.BonusEvent.PHONE_CONFIRMATION == promotion.bonus?.bonusEvent) {
                fastClickPaymentView.visibility = View.GONE
                btnAction.visibility = View.VISIBLE
                btnAction.text = context.getString(R.string._profile)
            } else if (fastClickPaymentSystem != null) {
                fastClickPaymentView.visibility = View.VISIBLE
                fastClickPaymentView.setButtonFastPaymentClickListener { isRebill: Boolean, amount: Int ->
                    if (promotion.id != null) {
                        buttonsClickListener?.onFastPaymentPayClicked(promotion.id!!, isRebill, amount)
                        if (isRebill) {
                            bonusFastClickPaymentHolder = this
                        }
                    }
                }

                // Calculate default 1click sum for bonus
                var defSum = promotion.bonus?.depositAmount

                promotion.bonus?.paidSumOfDeposits?.let { paidSum ->
                    defSum = defSum?.minus(paidSum)
                }
                defSum?.let {
                    if ( it < 1000.0 ) {
                        defSum = 1000.0
                    }
                }

                fastClickPaymentView.setFastClickPaymentSystem(fastClickPaymentSystem, defSum?.toString())
                btnAction.visibility = View.GONE
            } else {
                fastClickPaymentView.visibility = View.GONE
                btnAction.text = context.getString(R.string.bonus_receive)
                btnAction.visibility = View.VISIBLE
            }
        }

        private fun isProgressBonus(promotion: PromotionItem) : Boolean {
            val depositAmount = promotion.bonus?.depositAmount
            val paidSumOfDeposits = promotion.bonus?.paidSumOfDeposits
            return depositAmount != null && 0.0 != depositAmount && paidSumOfDeposits != null
        }

        private fun getDayAddition(num: Int): String {
            return when (num % 10) {
                1 -> "день"
                2, 3, 4 -> "дня"
                else -> "дней"
            }
        }

        private fun getMillisDiff(context: Context, dateEndText: String): Long {
            val serverDateFormat = DateTools.getServerLongDateFormat(context)
            serverDateFormat.timeZone = Calendar.getInstance(TimeZone.getTimeZone("UTC")).timeZone
            var dateEnd: Date? = null
            try {
                dateEnd = serverDateFormat.parse(dateEndText)
            } catch (e: ParseException) {
                Log.e(TAG, e.toString())
            }

            return if (dateEnd != null) {
                val currentTime = Calendar.getInstance().time
                val different = dateEnd.time - currentTime.time
                different
            } else {
                0
            }
        }

        private fun startTimer(context: Context, millisDiff: Long, holder: PromotionVH) {
            if (millisDiff > 0) {
                timer?.cancel()
                timer = object : CountDownTimer(millisDiff, 1000) {

                    override fun onTick(millisUntilFinished: Long) {
                        if (RecyclerView.NO_POSITION != holder.adapterPosition) {
                            //CountDownTimer onTick 2 times for different holder position for one timer
                            if ((holder.promotionItem?.bonus?.isActivated == true)
                                    || (holder.promotionItem?.bonus?.isDeactivated == true)) {
                                holder.tvDateEnd.text = DateTools.formatTime(millisUntilFinished)
                            }
                        }
                    }

                    override fun onFinish() {
                        holder.tvDateEnd.text = context.getString(R.string.finished)
                    }
                }.start()
                if (holder.promotionItem?.id != null) {
                    timers[holder.promotionItem!!.id!!] = timer!!
                }
            }
        }

        fun fastClickPaymentViewShowLoader() {
            fastClickPaymentView.showLoader()
        }

        fun fastClickPaymentViewShowSuccessfulRebillingMessage() {
            fastClickPaymentView.showSuccessfulRebillingMessage()
        }

        fun fastClickPaymentViewHideLoader() {
            fastClickPaymentView.hideLoader()
        }
    }

    inner class PermanentPromotionVH(val view: ItemPermanentPromotionBinding) :
        RecyclerView.ViewHolder(view.root) {

        private val permanentPromotionBackgroundTarget: Target = object : Target {
            override fun onBitmapLoaded(bitmap: Bitmap, from: LoadedFrom) {
                val roundedBitmapDrawable = RoundedBitmapDrawableFactory.create(view.root.context.resources, bitmap)
                roundedBitmapDrawable.cornerRadius = view.root.context.resources.getDimensionPixelSize(R.dimen.promotion_card_corner_radius).toFloat()
                view.cardRoot.background = roundedBitmapDrawable
            }

            override fun onBitmapFailed(e: java.lang.Exception, errorDrawable: Drawable?) {
                view.cardRoot.background = ContextCompat.getDrawable(view.root.context, R.drawable.promotion_card_default_bg)
            }

            override fun onPrepareLoad(placeHolderDrawable: Drawable?) {}
        }

        private val permanentPromotionImageCharacterTarget: Target = object : Target {
            override fun onBitmapLoaded(bitmap: Bitmap, from: LoadedFrom) {
                bitmap.let {
                    view.ivPromotion.scaleType = ImageView.ScaleType.CENTER_INSIDE
                    view.ivPromotion.adjustViewBounds = true
                    view.ivPromotion.setImageBitmap(it)
                }
            }

            override fun onBitmapFailed(e: java.lang.Exception, errorDrawable: Drawable) {}

            override fun onPrepareLoad(placeHolderDrawable: Drawable?) {}
        }

        fun bind(adapterPosition: Int, promotion: PromotionItem) {
            setTitle(promotion)
            view.btnAction.text = promotion.permanentPromotion?.buttonName
            if (promotion.permanentPromotion?.showInput == true) {
                view.btnAction.visibility = View.GONE
                view.promoCodeView.visibility = View.VISIBLE
                view.promoCodeView.listener = object : PromoCodeField.PromoCodeListener {
                    override fun onPromoCodeEntered(promoCode: String) {
                        if (promoCode.isNotEmpty()) {
                            promotion.id?.let { promotionId -> buttonsClickListener?.onTryBonusPromoCode(promotionId, promoCode) }
                        }
                    }
                }

                view.promoCodeView.setOnFocusChangeListener { isFocused ->
                    if (isFocused) {
                        buttonsClickListener?.onPromoCodeFocused(adapterPosition)
                    }
                }

            } else {
                view.btnAction.visibility = View.VISIBLE
                view.promoCodeView.visibility = View.GONE
            }
            
            val imgMob = promotion.permanentPromotion?.imgMob
            if (!imgMob.isNullOrEmpty()) {
                setPermanentBonusBackground(imgMob, view.root.context, view.cardRoot)
            } else {
                setPermanentBonusBackground(promotion.permanentPromotion?.imgPattern, view.root.context, view.cardRoot)
            }

            setPermanentBonusImageCharacter(promotion.permanentPromotion?.imgCharacter, view.ivPromotion)

            view.ibPromotionInfo.setOnClickListener {
                buttonsClickListener?.onInfoButtonClicked(promotion)
            }

            view.btnAction.setOnClickListener {
                buttonsClickListener?.onActionButtonClicked(ApoloConfig.getFullUrl(promotion.permanentPromotion?.buttonUrl))
            }
        }

        private fun setPermanentBonusBackground(imgPattern: String?, context: Context, cardRoot: View) {
            if (!TextUtils.isEmpty(imgPattern)) {
                Picasso.get().load(ApoloConfig.getFullUrl(imgPattern))
                    .into(permanentPromotionBackgroundTarget)
            } else {
                cardRoot.background = ContextCompat.getDrawable(context, R.drawable.promotion_card_default_bg)
            }
        }

        private fun setPermanentBonusImageCharacter(imgCharacter: String?, ivPromotion: ImageView) {
            if (!TextUtils.isEmpty(imgCharacter)) {
                Picasso.get().load(ApoloConfig.getFullUrl(imgCharacter))
                    .into(permanentPromotionImageCharacterTarget)
            } else {
                ivPromotion.setImageBitmap(null)
            }
        }

        private fun setTitle(promotion: PromotionItem) {
            var promotionName = promotion.permanentPromotion?.name
            promotionName = promotionName?.replace("</br>", "<br>")
            promotionName?.apply {
                view.tvName.setHtmlText(this)
            }
        }

        fun setError(promoError: String) {
            view.promoCodeView.showErrorMessage(promoError)

            val layoutParams: ViewGroup.LayoutParams = view.promoCodeView.layoutParams
            if (layoutParams is ViewGroup.MarginLayoutParams) {
                layoutParams.bottomMargin = 0
            }
        }
    }

    inner class FooterVH(inflater: LayoutInflater, parent: ViewGroup) :
            RecyclerView.ViewHolder(inflater.inflate(R.layout.view_footer, parent, false)) {

        fun bind() {
            val paddingBottom: Int = itemView.context.getResources().getDimensionPixelSize(R.dimen.not_organic_user_footer_padding_bottom)
            itemView.setPadding(0, 0, 0, paddingBottom)
            itemView.findViewById<View>(R.id.gl_payment_systems_view).setOnClickListener {
                footerListener?.onClickPaymentSystems()
            }
            itemView.findViewById<TextView>(R.id.tv_copyright_year)?.let {
                it.text = itemView.context.getString(
                    R.string.app_copyright_year,
                    YearRepository.getCurrentYear()
                )
            }
        }

    }

    interface ButtonsClickListener {
        fun onMainButtonClicked(bonusId: Int?, isBonusActivated: Boolean?, isEmailOrPhoneBonus: Boolean)
        fun onPromoCodeEntered(bonusId: Int, promoCode: String)
        fun onPromoCodeFocused(adapterPosition: Int)
        fun onInfoButtonClicked(promotion: PromotionItem)
        fun onDeactivatedButtonClicked(bonusId: Int, name: String, availableAfterDeactivation: Boolean)
        fun onFastPaymentPayClicked(bonusId: Int, isRebill: Boolean, amount: Int)
        fun onActionButtonClicked(url: String)
        fun onTryBonusPromoCode(promotionId: Int, promoCode: String)
    }
}
