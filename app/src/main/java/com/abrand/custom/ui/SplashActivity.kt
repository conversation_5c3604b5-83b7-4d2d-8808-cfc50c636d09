package com.abrand.custom.ui

import android.Manifest
import android.app.NotificationManager
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.os.CountDownTimer
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.net.toUri
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.abrand.custom.AutologinMutation
import com.abrand.custom.BuildConfig
import com.abrand.custom.data.ApoloConfig
import com.abrand.custom.data.Constants
import com.abrand.custom.data.JsonDataGenerator
import com.abrand.custom.data.Settings
import com.abrand.custom.data.entity.AnalyticsEvent
import com.abrand.custom.data.entity.InstallConfig
import com.abrand.custom.data.entity.User
import com.abrand.custom.data.entity.mapToLocalConfig
import com.abrand.custom.databinding.ActivitySplashBinding
import com.abrand.custom.interfaces.GenericTarget
import com.abrand.custom.network.OkHttpProcessor.AnalyticsListener
import com.abrand.custom.network.OkHttpProcessor.doRequest
import com.abrand.custom.network.OkHttpProcessor.sendAnalytics
import com.abrand.custom.network.PushTokenRepository
import com.abrand.custom.network.PushTokenRepository.PushUpdateListener
import com.abrand.custom.network.UpdateApp
import com.abrand.custom.presenter.FieldsErrorsHolder
import com.abrand.custom.tools.ExitService
import com.abrand.custom.tools.GeneralTools
import com.abrand.custom.ui.activitymain.MainActivity
import com.adjust.sdk.Adjust
import com.apollographql.apollo3.exception.ApolloException
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.gson.Gson
import kotlinx.coroutines.launch
import java.io.UnsupportedEncodingException
import java.net.URLDecoder

class SplashActivity : AppCompatActivity() {

    private var timer: CountDownTimer? = null
    private var updateImportancePassed = false
    private var pushAndConfigDataProcessed = false

    private val updateApp = UpdateApp(object : UpdateApp.Listener {
        override fun updateImportancePassed() {
            <EMAIL>()
        }
    })

    private var pushUrl: String? = null
    private var openLoginScreen = false
    private var bonusAction: String? = null
    private var bonusId: String? = null
    private var userId: String? = null
    private var hash: String? = null
    private var refCode: String? = null
    private var trackClick: String? = null
    private var loginToken: String? = null
    private var isFirstOpen = false
    private lateinit var viewModel: SplashViewModel
    private lateinit var binding: ActivitySplashBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivitySplashBinding.inflate(layoutInflater)
        setContentView(binding.root)

        viewModel = ViewModelProvider(this)[SplashViewModel::class.java]
        isFirstOpen = Settings.get().isFirstOpen

        processPushTokenUpdate()
        sendNotificationStatusAnalytics()
        startService(Intent(this, ExitService::class.java))

        if (GeneralTools.isNetworkConnected(this)) {
            updateApp.getUpdateImportance(this)
        } else {
            onUpdateImportancePassed()
        }

        observeViewModelEvents()
        waitForAdjustAdid()
    }

    private fun observeViewModelEvents() {
        lifecycleScope.launch {
            viewModel.eventFlow.collect { event ->
                when (event) {
                    SplashEffects.GeneratorSyncFinish -> onGeneratorServerCommunicationPassed()
                }
            }
        }
    }

    private fun waitForAdjustAdid() {
        if (Adjust.getAdid() == null) {
            timer = object : CountDownTimer(12000, 600) {
                override fun onTick(millisUntilFinished: Long) {
                    if (Adjust.getAdid() != null) {
                        timer?.cancel()
                        onAdidPassed()
                    }
                }

                override fun onFinish() {
                    onAdidPassed()
                }
            }.start()
        } else {
            onAdidPassed()
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == UpdateApp.REQUEST_WRITE_EXTERNAL_STORAGE) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                updateApp.startDownloading(this@SplashActivity)
            } else {
                if (ActivityCompat.shouldShowRequestPermissionRationale(
                        this,
                        Manifest.permission.WRITE_EXTERNAL_STORAGE
                    )
                ) {
                    updateApp.showWritePermissionRationaleDialog(this@SplashActivity)
                } else {
                    updateApp.showWritePermissionDeniedDialog(this@SplashActivity)
                }
            }
        }
    }

    @Suppress("OVERRIDE_DEPRECATION")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == UpdateApp.REQUEST_APP_SETTINGS) {
            val permissionCheck =
                ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE)
            if (permissionCheck == PackageManager.PERMISSION_GRANTED) {
                updateApp.startDownloading(this@SplashActivity)
            } else {
                updateApp.showWritePermissionDeniedDialog(this@SplashActivity)
            }
        }
    }

    override fun onDestroy() {
        updateApp.onDestroy()
        timer?.cancel()
        super.onDestroy()
    }

    private fun processPushTokenUpdate() {
        val settings = Settings.get()
        if (settings.isPushTokenMustBeUpdate && settings.userState == User.State.PLAYER) {
            val pushSubscriptionData = JsonDataGenerator().getPushSubscriptionAuthDataJson(
                BuildConfig.APPLICATION_ID,
                settings.pushToken,
                GeneralTools.getAndroidId(this)
            )
            PushTokenRepository().updateMobileAppSubscription(
                pushSubscriptionData,
                object : PushUpdateListener {
                    override fun onSuccess() {
                        settings.isPushTokenMustBeUpdate = false
                    }

                    override fun onFail() {
                        settings.isPushTokenMustBeUpdate = true
                    }
                })
        } else if (settings.isPushTokenMustBeUpdate && settings.userState != User.State.PLAYER) {
            settings.isPushTokenMustBeUpdate = false
        }
    }

    private fun sendNotificationStatusAnalytics() {
        val settings = Settings.get()

        val isNotificationEnabled: Boolean =
            GeneralTools.isNotificationEnables(this) &&
                    GeneralTools.getGeneralChannelImportance(this) != NotificationManager.IMPORTANCE_NONE

        if (isNotificationEnabled && !settings.isGeneralNotificationEnable) {
            sendAnalytics(
                this,
                AnalyticsEvent.PUSH_SUBSCRIPTION,
                JsonDataGenerator().getPushSubscriptionAnalyticsDataJson(Constants.ANALYTICS_PUSH_SUBSCRIPTION_ON_DATA),
                object : AnalyticsListener {
                    override fun onSuccess() {
                        Log.i(TAG, "sent notification on")
                        settings.isGeneralNotificationEnable = true
                    }

                    override fun onFailure() {
                        Log.i(TAG, "notification on failure")
                    }
                })
        } else if (!isNotificationEnabled && settings.isGeneralNotificationEnable) {
            sendAnalytics(
                this,
                AnalyticsEvent.PUSH_SUBSCRIPTION,
                JsonDataGenerator().getPushSubscriptionAnalyticsDataJson(Constants.ANALYTICS_PUSH_SUBSCRIPTION_OFF_DATA),
                object : AnalyticsListener {
                    override fun onSuccess() {
                        Log.i(TAG, "sent notification off")
                        settings.isGeneralNotificationEnable = false
                    }

                    override fun onFailure() {
                        Log.i(TAG, "notification off failure")
                    }
                })
        }
    }

    private fun processAssetConfig() {
        val json = GeneralTools.loadJSONFromAsset(this, ASSET_CONFIG_FILE_NAME)
        val installConfig = runCatching {
            Gson().fromJson(json, InstallConfig::class.java)
        }.getOrNull()

        if (installConfig != null) {
            val localConfig = installConfig.mapToLocalConfig()
            Settings.get().localConfig = localConfig

            if (!localConfig.salt.isNullOrEmpty()) {
                processLoginToken(localConfig.salt)
            } else {
                onPushAndConfigDataProcessed()
            }
        } else {
            onPushAndConfigDataProcessed()
        }
    }

    private fun sendFirstOpenAnalytics() {
        if (Settings.get().pushToken.isNotEmpty()) {
            sendAnalytics(this, AnalyticsEvent.FIRST_OPEN, null, null)
        } else {
            val postponedAnalyticsEvents = arrayListOf(AnalyticsEvent.FIRST_OPEN)
            Settings.get().postponedAnalyticsEvents = postponedAnalyticsEvents
        }
    }

    private fun onAdidPassed() {
        if (isFirstOpen) {
            sendFirstOpenAnalytics()
            processAssetConfig()
        } else {
            processDeepLink()
        }
    }

    private fun onUpdateImportancePassed() {
        checkGeneratorServer()
    }

    private fun checkGeneratorServer() {
        viewModel.onEvent(UIEvents.StartGeneratorSync)
    }

    private fun onGeneratorServerCommunicationPassed() {
        updateImportancePassed = true
        startMainActivityIfReady()
    }

    private fun onPushAndConfigDataProcessed() {
        Settings.get().isFirstOpen = false
        pushAndConfigDataProcessed = true
        startMainActivityIfReady()
    }

    private fun startMainActivityIfReady() {
        if (updateImportancePassed && pushAndConfigDataProcessed) {
            val intent = Intent(this, MainActivity::class.java).apply {
                putExtra(Constants.OPEN_LOGIN_SCREEN, openLoginScreen)
                putExtra(Constants.DEEP_LINK_URL, pushUrl)
                putExtra(Constants.BONUS_ACTION, bonusAction)
                putExtra(Constants.BONUS_ID, bonusId)
                putExtra(Constants.USER_ID, userId)
                putExtra(Constants.HASH, hash)
            }
            startActivity(intent)
            finish()
        }
    }

    private fun processDeepLink() {
        val extras = intent.extras

        if (extras != null) {
            refCode = extras.getString(Constants.REF_CODE)
            trackClick = extras.getString(Constants.TRACK_CLICK)
            loginToken = extras.getString(Constants.LOGIN_TOKEN)
            pushUrl = extras.getString(Constants.DEEP_LINK_URL)
            bonusAction = extras.getString(Constants.BONUS_ACTION)
            bonusId = extras.getString(Constants.BONUS_ID)
            userId = extras.getString(Constants.USER_ID)
            hash = extras.getString(Constants.HASH)

            processDeepLinkUri()

            refCode?.takeIf { refCode -> refCode.isNotEmpty() }?.let { Settings.get().refCode = it }
            trackClick?.takeIf { click -> click.isNotEmpty() }?.let { doRequest(it) }

            processLoginToken(loginToken)
        } else {
            onPushAndConfigDataProcessed()
        }
    }

    private fun processDeepLinkUri() {
        intent.data?.let { deepLinkUri ->
            trackClick = deepLinkUri.toString()

            val targetUrl = deepLinkUri.getQueryParameter(Constants.TARGET_URL) ?: return
            val targetUri = targetUrl.toUri()

            val targetUriQuery = try {
                if (targetUri.query != null) URLDecoder.decode(targetUri.query, "utf-8") else null
            } catch (e: UnsupportedEncodingException) {
                Log.d(TAG, "target decode: $e")
                null
            }

            targetUriQuery?.split("[?&]".toRegex())?.forEach { param ->
                val urlParameters = param.split("=".toRegex())
                if (urlParameters.size < 2) return@forEach // continue

                when (urlParameters[0]) {
                    Constants.URI_URL -> {
                        var uriPath = urlParameters[1]
                        if (uriPath.startsWith("/")) uriPath = uriPath.substring(1)

                        val uriParameters = uriPath.split("/")
                        if (uriParameters.getOrNull(0) == Constants.LOGIN_TOKEN_URL) {
                            loginToken = uriParameters.getOrNull(1)
                        }
                    }

                    Constants.REF_CODE_URL -> refCode = urlParameters[1]
                    Constants.REDIRECT_URL -> {
                        val redirectValue = urlParameters[1]
                        if (redirectValue.startsWith(Constants.DEEP_LINK_BONUS_ACTIVATE) ||
                            redirectValue.startsWith(Constants.DEEP_LINK_BONUS_RECEIVE)
                        ) {
                            pushUrl =
                                if (redirectValue.startsWith(Constants.DEEP_LINK_BONUS_ACTIVATE)) {
                                    ApoloConfig.getFullUrl(Constants.DEEP_LINK_BONUS_ACTIVATE)
                                } else {
                                    ApoloConfig.getFullUrl(Constants.DEEP_LINK_BONUS_RECEIVE)
                                }

                            val bonusParts = redirectValue.split("/")
                            bonusId = bonusParts.getOrNull(4)
                            userId = bonusParts.getOrNull(5)
                            hash = bonusParts.getOrNull(6)
                        } else {
                            pushUrl = ApoloConfig.getFullUrl(redirectValue)
                        }
                    }
                }
            }
        }
    }

    private fun processLoginToken(token: String?) {
        if (!token.isNullOrEmpty()) {
            viewModel.autoLogin(
                token,
                Settings.get().refCode,
                Adjust.getAdid(),
                object : GenericTarget<AutologinMutation.Viewer> {
                    override fun onSuccess(viewer: AutologinMutation.Viewer?) {
                        viewer?.profile?.let { profile ->
                            Settings.get().setLoggedUserEmail(profile.email)
                            Settings.get().setUserId(viewer.id)
                            FirebaseCrashlytics.getInstance().setUserId(viewer.id ?: "")
                            FirebaseAnalytics.getInstance(this@SplashActivity).setUserId(viewer.id)
                        }
                        onPushAndConfigDataProcessed()
                    }

                    override fun onError(
                        errorMessage: String,
                        errorCode: String,
                        fieldsErrors: FieldsErrorsHolder?
                    ) {
                        openLoginScreen = User.State.NOT_LOGGED == Settings.get().userState
                        onPushAndConfigDataProcessed()
                    }

                    override fun onFailure(e: ApolloException) {
                        openLoginScreen = User.State.NOT_LOGGED == Settings.get().userState
                        onPushAndConfigDataProcessed()
                    }
                })
        } else {
            onPushAndConfigDataProcessed()
        }
    }

    private companion object {
        private const val TAG = "SplashActivity"
        private const val ASSET_CONFIG_FILE_NAME = "config.json"
    }
}
