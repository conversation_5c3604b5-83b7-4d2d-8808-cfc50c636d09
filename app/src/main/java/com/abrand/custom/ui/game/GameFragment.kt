package com.abrand.custom.ui.game

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.util.Log
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.view.animation.AnimationUtils
import android.webkit.CookieManager
import android.webkit.WebChromeClient
import android.webkit.WebResourceRequest
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.FrameLayout
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.abrand.custom.GetFastClickPaymentSystemQuery
import com.abrand.custom.R
import com.abrand.custom.SubscribeBalanceSubscription
import com.abrand.custom.data.ApoloConfig
import com.abrand.custom.data.Resource
import com.abrand.custom.data.Settings
import com.abrand.custom.data.Status
import com.abrand.custom.data.entity.BonusRefund
import com.abrand.custom.data.entity.FastClickPaymentSystem
import com.abrand.custom.data.entity.ServerError
import com.abrand.custom.data.entity.Talisman
import com.abrand.custom.data.entity.User
import com.abrand.custom.databinding.FragmentGameBinding
import com.abrand.custom.interfaces.InGameListener
import com.abrand.custom.ui.activitymain.MainActivity
import com.abrand.custom.ui.payments.PaymentsFragment
import com.abrand.custom.ui.supportChat.SupportChatFragment
import com.apollographql.apollo3.exception.ApolloException
import com.google.firebase.crashlytics.FirebaseCrashlytics
import kotlinx.coroutines.launch

class GameFragment : Fragment(), InGameListener {
    var inGameMenuDialog: InGameMenuFragmentDialog = InGameMenuFragmentDialog()
    var isReplenishment: Boolean = false
        private set

    private var binding: FragmentGameBinding? = null
    private var url: String? = null
    private var fullUrl: String? = null
    var tournamentId: Int = 0
        private set
    var fastClickPaymentSystem: FastClickPaymentSystem? = null
        private set
    private var bonusRefund: BonusRefund? = null
    var talisman: Talisman? = null
        private set
    private val PAYMENTS_TAG = "#popup-payments"
    private val BONUS_REFUND_KEY = "bonusRefund"
    private var viewModel: GameViewModel? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        super.onCreate(savedInstanceState)
        binding = FragmentGameBinding.inflate(layoutInflater)

        setupInsets()

        viewModel = ViewModelProvider(this).get(GameViewModel::class.java)
        observeViewModel()

        val bundle = arguments
        if (bundle != null) {
            url = bundle.getString(URL_KEY)
            fullUrl = bundle.getString(FULL_URL_KEY)
            tournamentId = bundle.getInt(TOURNAMENT_ID, 0)
            fastClickPaymentSystem = bundle.getParcelable(FAST_CLICK_PAYMENT_SYSTEM)
        }

        inGameMenuDialog.listener = this
        addWebViewToFrame()

        viewModel!!.getTalisman()

        val activity: Activity? = activity
        binding!!.btnMenu.setOnClickListener { v: View? ->
            if (!inGameMenuDialog.isAdded && activity is MainActivity) {
                inGameMenuDialog.show(
                    activity.supportFragmentManager,
                    InGameMenuFragmentDialog.TAG
                )
                val handler = Handler(Looper.getMainLooper())
                handler.postDelayed(
                    { binding!!.ivMenu.visibility = View.INVISIBLE },
                    300
                )
            }
        }

        if (savedInstanceState != null) {
            bonusRefund = savedInstanceState.getParcelable(BONUS_REFUND_KEY)
        }

        setOnBackPressed()

        return binding!!.root
    }

    private fun addWebViewToFrame() {
        val webView = createWebView(requireContext(), url!!)

        try {
            binding!!.frameWebView.addView(webView)
        } catch (stateException: IllegalStateException) {
            try {
                val parent = webView!!.parent
                if (parent is FrameLayout) {
                    parent.removeView(webView)
                }
                binding!!.frameWebView.addView(webView)
            } catch (e: NullPointerException) {
                FirebaseCrashlytics.getInstance().recordException(e)
            } catch (e: ClassCastException) {
                FirebaseCrashlytics.getInstance().recordException(e)
            } catch (e: IllegalStateException) {
                throw IllegalStateException("After try to remove the webView from parent", e)
            }
        }
    }

    override fun onStart() {
        super.onStart()
        viewModel?.balanceSubscribe()
        viewModel?.messagesSubscribe()
        viewModel?.bonusBalanceWonSubscribe()

        val activity: Activity? = activity
        if (activity is MainActivity) {
            activity.hideToolbar()
        }
    }

    override fun onStop() {
        super.onStop()
        viewModel?.balanceUnSubscribe()
        viewModel?.messagesUnSubscribe()
        viewModel?.bonusBalanceWonUnsubscribe()
    }

    override fun onDestroyView() {
        val activity: Activity? = activity
        if (activity is MainActivity) {
            val isSupportChatFragment = activity.currentFragment is SupportChatFragment
            if (!isSupportChatFragment) {
                activity.showToolbar()
            }
        }
        if (inGameMenuDialog.isVisible) {
            inGameMenuDialog.dismissAndReset()
        }
        binding!!.frameWebView.removeAllViews()
        binding = null
        super.onDestroyView()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putParcelable(BONUS_REFUND_KEY, bonusRefund)
    }

    private fun setupInsets() {
        ViewCompat.setOnApplyWindowInsetsListener(binding!!.frameWebView) { view: View?, insets: WindowInsetsCompat ->
            val layoutParams = binding!!.frameWebView.layoutParams
            if (layoutParams is MarginLayoutParams) {
                layoutParams.topMargin = insets.systemWindowInsetTop
                layoutParams.bottomMargin = insets.systemWindowInsetBottom
            }

            if (inGameMenuDialog.isResumed) {
                if (insets.systemWindowInsetBottom > 150) {
                    inGameMenuDialog.onKeyboardShow()
                } else {
                    inGameMenuDialog.onKeyboardHide()
                }
            }
            insets
        }

        ViewCompat.setOnApplyWindowInsetsListener(binding!!.btnMenu) { view: View?, insets: WindowInsetsCompat ->
            val layoutParams = binding!!.btnMenu.layoutParams
            if (layoutParams is MarginLayoutParams) {
                layoutParams.topMargin =
                    resources.getDimensionPixelSize(R.dimen.size_16) + insets.systemWindowInsetTop
                layoutParams.bottomMargin =
                    resources.getDimensionPixelSize(R.dimen.size_16) + insets.systemWindowInsetBottom
                binding!!.btnMenu.layoutParams = layoutParams
            }
            insets
        }
    }

    private fun observeViewModel() {
        viewModel!!.fastPaymentRebillingLiveData.observe(
            viewLifecycleOwner,
            fastPaymentRebillingObserver
        )
        viewModel!!.fastPaymentUrlLiveData.observe(viewLifecycleOwner, fastPaymentUrlObserver)
        viewModel!!.viewerWalletLiveData.observe(viewLifecycleOwner, viewerWalletObserver)
        viewModel!!.fastClickPaymentSystemLiveData.observe(
            viewLifecycleOwner,
            fastClickPaymentSystemObserver
        )
        viewModel!!.makeRebillLiveData.observe(viewLifecycleOwner, makeRebillObserver)
        viewModel!!.talismanLiveData.observe(viewLifecycleOwner, talismanObserver)
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel?.sideEffectFlow?.collect { effect ->
                    when(effect) {
                        is GameViewModel.SideEffect.BonusBalanceWonEffect -> {
                            val dialog = IngamePopupFragment()
                            dialog.arguments = Bundle().apply {
                                putInt("id", effect.balance.id)
                                putString("value", effect.balance.value)
                            }
                            dialog.show(parentFragmentManager, "BonusPopup")
                        }
                    }
                }
            }
        }
    }

    private val fastPaymentRebillingObserver =
        Observer { isRebilling: Boolean ->
            if (isRebilling) {
                inGameMenuDialog.showFastClickPaymentLoader()
            } else {
                inGameMenuDialog.showFastClickPaymentSuccessfulRebillingMessage()
                if (url == fullUrl) {
                    viewModel!!.webViewGame!!.loadUrl(url!!)
                }
            }
        }

    private val fastPaymentUrlObserver =
        Observer<String> { depositUrl: String? -> this.openDepositScreen(depositUrl) }

    private val viewerWalletObserver =
        Observer { viewerWallet: SubscribeBalanceSubscription.ViewerWallet? ->
            if (viewerWallet == null) return@Observer
            try {
                val bonusBetSum = (viewerWallet.bonusBetSum as String).toDouble()
                val bonusRefundSum = (viewerWallet.bonusRefundSum as String).toDouble()
                val bonusPercentToRefund = viewerWallet.bonusPercentToRefund.toInt()
                bonusRefund =
                    BonusRefund(bonusBetSum, bonusRefundSum, bonusPercentToRefund.toDouble())
                val activity: Activity? = activity
                activity?.runOnUiThread { inGameMenuDialog.setBonusRefund() }
            } catch (e: NullPointerException) {
                //NOP
                // TODO: 08.12.2022 refactor to not invoke that case
            }
        }

    private val fastClickPaymentSystemObserver =
        Observer<GetFastClickPaymentSystemQuery.FastClickPaymentSystem> { data: GetFastClickPaymentSystemQuery.FastClickPaymentSystem? ->
            isReplenishment = true
            if (url == fullUrl && data != null) {
                inGameMenuDialog.updateFastClickPaymentSystem(FastClickPaymentSystem(data))
            } else {
                inGameMenuDialog.showPlayMoneyBtn()
            }
        }

    private val makeRebillObserver =
        Observer<Resource<String, ServerError, ApolloException>> { responseResource: Resource<String?, ServerError?, ApolloException?> ->
            when (responseResource.status) {
                Status.SUCCESS -> {}
                Status.ERROR -> inGameMenuDialog.hideFastClickPaymentLoader()
                Status.FAILURE -> if (responseResource.failure != null) {
                    Log.e(TAG, responseResource.failure.toString())
                }

                else -> {}
            }
        }

    private val talismanObserver =
        Observer<Resource<Talisman?, ServerError, ApolloException>> { responseResource: Resource<Talisman?, ServerError?, ApolloException?> ->
            when (responseResource.status) {
                Status.SUCCESS -> this.talisman = responseResource.data
                Status.ERROR -> {
                    val serverError = responseResource.error
                    if (serverError != null) {
                        Log.w(TAG, serverError.errorMessage)
                        // Does user need to see this error?
//                    Toast.makeText(this, serverError.getErrorMessage(), Toast.LENGTH_LONG).show();
                    }
                }

                Status.FAILURE -> {
                    val apolloException = responseResource.failure
                    if (apolloException != null) {
                        Log.w(TAG, apolloException.toString())
                        // Does user need to see that exception?
//                    Toast.makeText(this, apolloException.toString(), Toast.LENGTH_LONG).show();
                    }
                }

                else -> {}
            }
        }

    private fun setOnBackPressed() {
        binding!!.root.setOnKeyListener { v: View?, keyCode: Int, event: KeyEvent? ->
            if (keyCode == KeyEvent.KEYCODE_BACK && viewModel!!.webViewGame!!.canGoBack()) {
                viewModel!!.webViewGame!!.goBack()
                return@setOnKeyListener true
            }
            false
        }
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun createWebView(context: Context, gameUrl: String): WebView? {
        if (viewModel!!.webViewGame == null) {
            val webView = WebView(context)
            webView.layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )

            setupWV(webView)
            viewModel!!.webViewGame = webView
            webView.loadUrl(gameUrl)
            return webView
        } else {
            val webView = viewModel!!.webViewGame

            webView!!.webViewClient = ViewClient()

            return viewModel!!.webViewGame
        }
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun setupWV(webView: WebView) {
        val cookieManager = CookieManager.getInstance()

        webView.isFocusable = true
        webView.isFocusableInTouchMode = true

        webView.webChromeClient = WebChromeClient()

        webView.settings.allowFileAccessFromFileURLs = true
        webView.settings.allowUniversalAccessFromFileURLs = true
        webView.settings.domStorageEnabled = true
        webView.settings.javaScriptEnabled = true
        webView.settings.allowFileAccess = true

        cookieManager.setAcceptCookie(true)
        //In Lower than LOLLIPOP(including LOLLIPOP) Third party cookies are enabled by default
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.LOLLIPOP) {
            cookieManager.setAcceptThirdPartyCookies(webView, true)
        }

        // Should be call after rotate
        webView.webViewClient = ViewClient()

        webView.setBackgroundColor(Color.BLACK)
    }

    override fun openDepositClicked() {
        openDepositScreen("")
    }

    override fun openSupportChatClicked() {
        openSupportChat()
    }

    override fun openRegister() {
        val activity: Activity? = activity
        if (activity is MainActivity) {
            activity.showEnterScreen(false)
        }
    }

    override fun onDialogClosed() {
        if (binding != null) {
            binding!!.ivMenu.visibility = View.VISIBLE
            val enlargeAnimation = AnimationUtils.loadAnimation(
                context, R.anim.rotate_btn_to_plus
            )
            binding!!.ivMenu.startAnimation(enlargeAnimation)
        }
    }

    override fun makeRebill(amount: Int) {
        viewModel!!.makeRebill(amount)
    }

    override fun getFastPaymentUrl(amount: Int) {
        viewModel!!.getFastPaymentUrl(amount)
    }

    internal inner class ViewClient : WebViewClient() {
        override fun shouldOverrideUrlLoading(view: WebView, request: WebResourceRequest): Boolean {
            val url = request.url.toString()
            return shouldOverrideUrl(view, url)
        }

        //Makes overrides deprecated API warning
        override fun shouldOverrideUrlLoading(view: WebView, url: String): Boolean {
            return shouldOverrideUrl(view, url)
        }

        private fun shouldOverrideUrl(view: WebView, url: String): Boolean {
            if ((ApoloConfig.BASE_URL) == url) {
                openHomeScreen()
                return true
            } else if (!TextUtils.isEmpty(url) && url.startsWith(ApoloConfig.BASE_URL)) {
                if (url.contains(PAYMENTS_TAG)) {
                    if (User.State.PLAYER == Settings.get().userState) {
                        openDepositScreen("")
                    } else {
                        openRegister()
                    }
                }
                return true
            }

            return false
        }
    }

    fun openHomeScreen() {
        val activity: Activity? = activity
        if (activity is MainActivity) {
            activity.openHomeScreen()
        }
    }

    fun openCurrentTournament() {
        val activity: Activity? = activity
        if (activity is MainActivity) {
            activity.showTournamentInternal(tournamentId)
        }
    }

    fun openDepositScreen(depositUrl: String?) {
        val activity: Activity? = activity
        if (activity is MainActivity) {
            activity.openPaymentsScreen(PaymentsFragment.Screen.DEPOSIT, depositUrl)
        }
    }

    fun openSupportChat() {
        val activity: Activity? = activity
        if (activity is MainActivity) {
            activity.openSupportChat(url)
        }
    }

    override fun loadMoneyGame() {
        url = fullUrl
        viewModel!!.webViewGame!!.loadUrl(fullUrl!!)

        val handler = Handler(Looper.getMainLooper())
        handler.postDelayed({ viewModel!!.webViewGame!!.clearHistory() }, 500)
    }

    val isDemoGame: Boolean
        get() = url != fullUrl

    companion object {
        private val TAG: String = GameFragment::class.java.simpleName
        const val URL_KEY: String = "ukey"
        const val FULL_URL_KEY: String = "full_ukey"
        const val TOURNAMENT_ID: String = "tournamentId"
        const val FAST_CLICK_PAYMENT_SYSTEM: String = "fastClickPaymentSystem"
    }
}
