package com.abrand.custom.ui.activitymain

import android.Manifest
import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Intent
import android.content.pm.ActivityInfo
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.util.Log
import android.view.LayoutInflater
import android.view.Menu
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.webkit.CookieManager
import android.webkit.WebChromeClient
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.Button
import android.widget.ExpandableListView
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.RequiresApi
import androidx.appcompat.app.ActionBarDrawerToggle
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.compose.foundation.layout.Box
import androidx.compose.material3.MaterialTheme
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import androidx.core.view.GravityCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.drawerlayout.widget.DrawerLayout
import androidx.drawerlayout.widget.DrawerLayout.DrawerListener
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.NavController
import androidx.navigation.NavDestination
import androidx.navigation.NavOptions
import androidx.navigation.Navigation
import androidx.navigation.ui.AppBarConfiguration
import androidx.navigation.ui.NavigationUI
import androidx.recyclerview.widget.DividerItemDecoration
import com.abrand.custom.BuildConfig
import com.abrand.custom.GetDrawerBannerQuery
import com.abrand.custom.GetFastClickPaymentSystemQuery
import com.abrand.custom.GetInitialViewerDataQuery
import com.abrand.custom.R
import com.abrand.custom.SubscribeBalanceSubscription
import com.abrand.custom.SubscribeLoyaltyPointsSubscription
import com.abrand.custom.SubscribeLoyaltyProgressSubscription
import com.abrand.custom.SubscribeLoyaltyStatusSubscription
import com.abrand.custom.SubscribeLoyaltyXOnPointsSubscription
import com.abrand.custom.SubscribeMessagesSubscription
import com.abrand.custom.SubscribeRealTimeNotificationSubscription
import com.abrand.custom.data.ApoloConfig
import com.abrand.custom.data.Constants
import com.abrand.custom.data.JsonDataGenerator
import com.abrand.custom.data.Resource
import com.abrand.custom.data.Settings
import com.abrand.custom.data.Status
import com.abrand.custom.data.entity.AnalyticsEvent
import com.abrand.custom.data.entity.AuthResponse
import com.abrand.custom.data.entity.BonusRefund
import com.abrand.custom.data.entity.ExpandedMenuModel
import com.abrand.custom.data.entity.FastClickPaymentSystem
import com.abrand.custom.data.entity.FirebaseEvents
import com.abrand.custom.data.entity.LocalGameItem
import com.abrand.custom.data.entity.LocalNews
import com.abrand.custom.data.entity.LoginType
import com.abrand.custom.data.entity.LoyaltyStatus
import com.abrand.custom.data.entity.MessageItem
import com.abrand.custom.data.entity.NewsItem
import com.abrand.custom.data.entity.ResponseException
import com.abrand.custom.data.entity.Screen
import com.abrand.custom.data.entity.ServerError
import com.abrand.custom.data.entity.SessionDataHolder
import com.abrand.custom.data.entity.SocialNetwork
import com.abrand.custom.data.entity.SupportPhone
import com.abrand.custom.data.entity.TournamentsItem
import com.abrand.custom.data.entity.UnvisitedCount
import com.abrand.custom.data.entity.UrlSource
import com.abrand.custom.data.entity.User
import com.abrand.custom.data.entity.UserBalance
import com.abrand.custom.data.entity.XOnPointsEvent
import com.abrand.custom.data.entity.lottery.LocalLottery
import com.abrand.custom.databinding.ActivityMainBinding
import com.abrand.custom.databinding.NavHeaderMainBinding
import com.abrand.custom.fragment.FragmentXOnPoints
import com.abrand.custom.fragment.GameThumb
import com.abrand.custom.interfaces.GenericTarget
import com.abrand.custom.interfaces.GetRegistrationBannerTarget
import com.abrand.custom.network.ApolloProcessorKt.Companion.bonusActivateWithHash
import com.abrand.custom.network.ApolloProcessorKt.Companion.bonusReceiveWithHash
import com.abrand.custom.network.ApolloProcessorKt.Companion.getNewsItemByUrl
import com.abrand.custom.network.OkHttpProcessor.L4PAuthListener
import com.abrand.custom.network.OkHttpProcessor.sendAnalytics
import com.abrand.custom.network.OkHttpProcessor.sendSocialTokenToL4P
import com.abrand.custom.network.UserAgentInterceptor.Companion.userAgent
import com.abrand.custom.presenter.FieldsErrorsHolder
import com.abrand.custom.social.TelegramNetwork
import com.abrand.custom.social.YandexNetwork
import com.abrand.custom.tools.DateTools
import com.abrand.custom.tools.GeneralTools
import com.abrand.custom.tools.MessengerHelper
import com.abrand.custom.tools.MessengerHelper.MessengerCallback
import com.abrand.custom.tools.MessengerHelper.MessengerError
import com.abrand.custom.tools.NotificationUtil.createNotificationChannel
import com.abrand.custom.tools.NotificationUtil.showNotificationsPermissionDeniedDialog
import com.abrand.custom.tools.readTextFromAsset
import com.abrand.custom.ui.enter.EnterFragment
import com.abrand.custom.ui.exchangepoints.ExchangePointsFragment
import com.abrand.custom.ui.game.GameFragment
import com.abrand.custom.ui.home.GamesAdapter.GameTab
import com.abrand.custom.ui.home.HomeFragment
import com.abrand.custom.ui.interfaces.UserStateListener
import com.abrand.custom.ui.loyaltyprogram.LoyaltyProgramFragment
import com.abrand.custom.ui.loyaltyprogram.NewLoyaltyStatusFragment
import com.abrand.custom.ui.messages.MessagesFragment
import com.abrand.custom.ui.newpassword.NewPasswordFragment
import com.abrand.custom.ui.news.NewsInternalFragment
import com.abrand.custom.ui.noconnection.NoConnectionFragment
import com.abrand.custom.ui.payments.LinkFragment
import com.abrand.custom.ui.payments.PaymentsFragment
import com.abrand.custom.ui.pregame.PregameFragment
import com.abrand.custom.ui.promotions.PromotionsFragment
import com.abrand.custom.ui.shop.ShopFragment
import com.abrand.custom.ui.views.BonusWageringDrawerView
import com.abrand.custom.ui.views.BonusWageringView
import com.abrand.custom.ui.views.FastClickPaymentView
import com.abrand.custom.ui.views.FastClickPaymentViewBottom
import com.abrand.custom.ui.wheeloffortune.WheelOfFortuneFragment
import com.apollographql.apollo3.exception.ApolloException
import com.apollographql.apollo3.exception.ApolloHttpException
import com.apollographql.apollo3.exception.ApolloNetworkException
import com.facebook.AccessToken
import com.facebook.CallbackManager
import com.facebook.FacebookCallback
import com.facebook.FacebookException
import com.facebook.login.LoginManager
import com.facebook.login.LoginResult
import com.google.android.material.internal.NavigationMenuView
import com.google.android.material.snackbar.Snackbar
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.squareup.picasso.Picasso
import com.vk.api.sdk.VK.onActivityResult
import com.vk.api.sdk.auth.VKAccessToken
import com.vk.api.sdk.auth.VKAuthCallback
import com.yandex.authsdk.YandexAuthException
import org.telegram.passport.TelegramPassport
import java.net.MalformedURLException
import java.net.URL
import java.util.Locale
import java.util.Timer
import java.util.TimerTask
import kotlin.math.floor

class MainActivity : AppCompatActivity(), GetRegistrationBannerTarget, UserStateListener {
    private var binding: ActivityMainBinding? = null
    private var mAppBarConfiguration: AppBarConfiguration? = null
    private var navController: NavController? = null
    private var toolbar: Toolbar? = null
    private var mainViewModel: MainViewModel? = null
    private var mainViewModelKt: MainViewModelKt? = null
    private var drawer: DrawerLayout? = null
    private var tvUserNameDrawer: TextView? = null
    private var headerMessagesCounter: ViewGroup? = null
    private var tvHeaderMessagesCounter: TextView? = null
    private var bonusWageringView: BonusWageringDrawerView? = null
    private var tvBalanceDrawer: TextView? = null
    private var tvRealBalanceDrawer: TextView? = null
    private var tvBonusBalanceDrawer: TextView? = null
    private var tvDrawerBalancePoints: TextView? = null
    private var tvDrawerXOnPoints: TextView? = null
    private var llDrawerXOnPointsInfo: LinearLayout? = null
    private var tvXOnPointsInfoDescription: TextView? = null
    private var tvXOnPointsToEnd: TextView? = null
    private var tvDrawerCashback: TextView? = null
    private var currencyCode: String? = null
    private var btnReplenishBalance: View? = null
    private var fastClickPaymentView: FastClickPaymentView? = null
    private var fastClickPaymentViewBottom: FastClickPaymentViewBottom? = null
    private var isFastPay = false
    private var isToolbarBtnsClickable = true
    private var tvDrawerOtherMethods: View? = null
    var fastClickPaymentSystem: FastClickPaymentSystem? = null
        private set
    var balance: Double = 0.0
        private set
    var realBalance: Double = 0.0
        private set
    private var loyaltyPoints = 0.0
    var loyaltyStatusId: Int = 0
        private set
    var loyaltyProgressPercent: Int = 0
        private set
    var bonusRefund: BonusRefund? = null
        set(bonusRefund) {
            field = bonusRefund
            if (bonusRefund != null && bonusRefund.bonusRefundSum > 0) {
                bonusWageringView!!.setBetRefundSum(
                    bonusRefund.bonusBetSum,
                    bonusRefund.bonusRefundSum
                )
                bonusWageringView!!.setPercentToRefund(bonusRefund.bonusPercentToRefund)
                bonusWageringView!!.listener = object : BonusWageringView.Listener {
                    override fun onResetBonusBalanceClicked() {
                        mainViewModelKt!!.resetBonusBalance()
                    }
                }
                showBonusWagering()
            } else {
                hideBonusWagering()
            }
        }
    private var menuAdapter: NavMenuAdapter? = null
    private var navHeader: View? = null
    private var tvBottomBtnBalance: TextView? = null
    private var btnBottomBalance: ConstraintLayout? = null
    private var btnBottom: Button? = null
    private var containerBottomBtn: LinearLayout? = null
    private var containerSystemMessages: LinearLayout? = null
    private var dialogCloudflare: Dialog? = null
    private var cloudflareIsResolving = false
    private val callbackManager: CallbackManager = CallbackManager.Factory.create()
    private var systemWindowInsetTop = 0
    private var requestPermissionNotificationLauncher: ActivityResultLauncher<String>? = null
    private val screensWithBottomBtn = listOf(
        R.id.nav_home,
        R.id.nav_promotions,
        R.id.nav_game_room,
        R.id.nav_news,
        R.id.nav_news_internal,
        R.id.nav_loyalty_program,
        R.id.nav_tournaments,
        R.id.nav_tournament_internal,
        R.id.nav_lottery_list,
        R.id.nav_lottery_internal,
        R.id.nav_hall_of_fame,
    )
    private val screensWithFastPay = listOf(
        R.id.nav_home,
        R.id.nav_promotions,
        R.id.nav_game_room,
        R.id.nav_news,
        R.id.nav_news_internal,
        R.id.nav_loyalty_program,
        R.id.nav_tournaments,
        R.id.nav_tournament_internal,
    )
    private var firebaseAnalytics: FirebaseAnalytics? = null
    private var drawerToggle: ActionBarDrawerToggle? = null

    @SuppressLint("RestrictedApi")
    public override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        resources.configuration.setLocale(Locale.forLanguageTag("ru"))
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding!!.root)

        firebaseAnalytics = FirebaseAnalytics.getInstance(this)

        mainViewModel = ViewModelProvider(this).get(MainViewModel::class.java)
        mainViewModelKt = ViewModelProvider(this).get(MainViewModelKt::class.java)

        toolbar = binding!!.appBar.toolbar
        setSupportActionBar(toolbar)
        binding!!.appBar.ablAppbar.bringToFront()

        drawer = binding!!.drawerLayout
        val navigationView = binding!!.navView
        // Passing each menu ID as a set of Ids because each
        // menu should be considered as top level destinations.
        mAppBarConfiguration = AppBarConfiguration.Builder(
            R.id.nav_home,
            R.id.nav_promotions,
            R.id.nav_tournaments,
            R.id.nav_game_room,
            R.id.nav_payments,
            R.id.nav_shop,
            R.id.nav_lottery_list,
            R.id.nav_wheel_of_fortune,
            R.id.nav_terms,
            R.id.nav_pregameFragment,
            R.id.nav_profile,
            R.id.nav_enter,
            R.id.nav_news,
            R.id.nav_messages,
            R.id.nav_loyalty_program,
            R.id.nav_exchange_points,
            R.id.nav_no_connection,
            R.id.nav_cashback,
            R.id.nav_hall_of_fame
        )
            .setDrawerLayout(drawer)
            .build()
        navController = Navigation.findNavController(this, R.id.nav_host_fragment)
        NavigationUI.setupActionBarWithNavController(
            this,
            navController!!, mAppBarConfiguration!!
        )
        NavigationUI.setupWithNavController(navigationView, navController!!)

        menuAdapter = NavMenuAdapter(this)
        binding!!.navigationMenu.setAdapter(menuAdapter)

        val navHeaderBinding = NavHeaderMainBinding.inflate(LayoutInflater.from(this), null, false)

        navHeader = navHeaderBinding.root
        binding!!.navigationMenu.addHeaderView(navHeader)
        navHeaderBinding.root.addOnAttachStateChangeListener(object : View.OnAttachStateChangeListener {
            override fun onViewAttachedToWindow(v: View) {
                navHeaderBinding.root.removeOnAttachStateChangeListener(this)

                val composeView = ComposeView(this@MainActivity).apply {
                    layoutParams = FrameLayout.LayoutParams(
                        FrameLayout.LayoutParams.WRAP_CONTENT,
                        FrameLayout.LayoutParams.MATCH_PARENT
                    )
                    setViewCompositionStrategy(
                        ViewCompositionStrategy.DisposeOnDetachedFromWindow
                    )
                    setContent {
                        MaterialTheme {
                            Box {
                                //content
                                NumberInRing()
                            }
                        }
                    }
                }
                // Добавляем ComposeView в контейнер
                navHeaderBinding.bonusBalancesCounterComposeContainer.removeAllViews()
                navHeaderBinding.bonusBalancesCounterComposeContainer.addView(composeView)
            }

            override fun onViewDetachedFromWindow(v: View) { /* Ничего не делаем */ }
        })

        btnBottomBalance = findViewById(R.id.btnBottomBalance)
        btnBottom = findViewById(R.id.btnBottom)
        tvBottomBtnBalance = findViewById(R.id.tvBalance)
        containerBottomBtn = findViewById(R.id.containerBottomBtn)
        containerSystemMessages = findViewById(R.id.containerSystemMessages)

        tvUserNameDrawer = navHeaderBinding.tvDrawerUserName
        headerMessagesCounter = navHeaderBinding.headerMessagesCounter
        tvHeaderMessagesCounter = navHeaderBinding.tvHeaderMessagesCounter
        tvBalanceDrawer = navHeaderBinding.tvDrawerBalance
        bonusWageringView = navHeaderBinding.bonusWagering
        tvRealBalanceDrawer = navHeader?.findViewById(R.id.tv_drawer_balance_real_amount)
        tvBonusBalanceDrawer = navHeader?.findViewById(R.id.tv_drawer_balance_bonus_amount)
        tvDrawerBalancePoints = navHeader?.findViewById(R.id.tv_drawer_balance_points)
        tvDrawerXOnPoints = navHeader?.findViewById(R.id.tv_drawer_x_on_points)
        llDrawerXOnPointsInfo = navHeader?.findViewById(R.id.ll_drawer_x_on_points_info)
        tvXOnPointsInfoDescription = navHeader?.findViewById(R.id.tv_x_on_points_info_description)
        tvXOnPointsToEnd = navHeader?.findViewById(R.id.tv_x_on_points_to_end)
        tvDrawerCashback = navHeader?.findViewById(R.id.tv_drawer_cashback)
        fastClickPaymentView = navHeader?.findViewById(R.id.fast_click_payment)
        fastClickPaymentView?.setButtonFastPaymentClickListener(buttonFastPaymentClickListener)
        fastClickPaymentViewBottom = findViewById(R.id.containerBottomFastPay)
        fastClickPaymentViewBottom?.setButtonFastPaymentClickListener(
            buttonFastPaymentBottomClickListener
        )
        btnReplenishBalance = navHeader?.findViewById(R.id.btn_replenish_balance)
        btnReplenishBalance?.setOnClickListener(View.OnClickListener { v: View? ->
            drawer!!.closeDrawers()
            openPaymentsScreen(PaymentsFragment.Screen.DEPOSIT)
        })
        tvDrawerOtherMethods = navHeader?.findViewById(R.id.tv_drawer_other_methods)
        tvDrawerOtherMethods?.setOnClickListener(View.OnClickListener { v: View? ->
            drawer!!.closeDrawers()
            openPaymentsScreen(PaymentsFragment.Screen.DEPOSIT)
        })
        navHeader?.findViewById<View>(R.id.btn_drawer_exchange_points)
            ?.setOnClickListener { v: View? ->
                drawer!!.closeDrawers()
                openScreen(R.id.nav_exchange_points)
            }
        navHeader?.findViewById<View>(R.id.header_messages)?.setOnClickListener { v: View? ->
            drawer!!.closeDrawers()
            openScreen(R.id.nav_messages)
        }

        navController!!.addOnDestinationChangedListener { controller: NavController?, destination: NavDestination, arguments: Bundle? ->
            applyState(Settings.get().userState)
            updateToolbar(destination.id)
            applyBottomBtnVisibilityState(destination.id)

            if (R.id.nav_home == destination.id) {
                menuAdapter!!.setMenuItemSelected(ExpandedMenuModel.MenuItem.HOME)
            } else if (R.id.nav_promotions == destination.id) {
                menuAdapter!!.setMenuItemSelected(ExpandedMenuModel.MenuItem.PROMOTIONS)
                menuAdapter!!.setPromotionsCounter(0)
                mainViewModelKt!!.markPromotionsVisited()
            } else if (R.id.nav_tournaments == destination.id) {
                menuAdapter!!.setMenuItemSelected(ExpandedMenuModel.MenuItem.TOURNAMENTS)
                menuAdapter!!.setTournamentsCounter(0)
                mainViewModelKt!!.markTournamentsVisited()
            } else if (R.id.nav_game_room == destination.id) {
                menuAdapter!!.setMenuItemSelected(ExpandedMenuModel.MenuItem.GAME_ROOM)
            } else if (R.id.nav_shop == destination.id) {
                menuAdapter!!.setMenuItemSelected(ExpandedMenuModel.MenuItem.SHOP)
            } else if (R.id.nav_lottery_list == destination.id) {
                menuAdapter!!.setMenuItemSelected(ExpandedMenuModel.MenuItem.LOTTERY)
                menuAdapter!!.setLotteriesCounter(0)
                mainViewModelKt!!.markLotteriesVisited()
            } else if (R.id.nav_payments == destination.id) {
                menuAdapter!!.setMenuItemSelected(ExpandedMenuModel.MenuItem.CASH_BOX)
            } else if (R.id.nav_wheel_of_fortune == destination.id) {
                menuAdapter!!.setMenuItemSelected(ExpandedMenuModel.MenuItem.WHEEL_OF_FORTUNE)
            } else if (R.id.nav_terms == destination.id) {
                menuAdapter!!.setMenuItemSelected(ExpandedMenuModel.MenuItem.RULES)
            } else if (R.id.nav_news == destination.id) {
                menuAdapter!!.setMenuItemSelected(ExpandedMenuModel.MenuItem.NEWS)
            } else if (R.id.nav_messages == destination.id) {
                menuAdapter!!.setMenuItemSelected(ExpandedMenuModel.MenuItem.MESSAGES)
            } else if (R.id.nav_loyalty_program == destination.id) {
                menuAdapter!!.setMenuItemSelected(ExpandedMenuModel.MenuItem.LOYALTY_PROGRAM)
            } else if (R.id.nav_hall_of_fame == destination.id) {
                menuAdapter!!.setMenuItemSelected(ExpandedMenuModel.MenuItem.HALL_OF_FAME)
            } else {
                menuAdapter!!.setMenuItemSelected(null)
            }

            setOrientation(destination.id)
            processPostponedLoyaltyStatus(destination.id)
        }

        processDeepLink()
        processOpenIntentScreen()

        setBalanceAndEmailPlaceholder()
        binding!!.navFooterView.tvVersionInfo.text =
            getString(R.string.version_info, BuildConfig.VERSION_NAME, BuildConfig.VERSION_CODE)

        val navigationMenuView = navigationView.getChildAt(0) as NavigationMenuView
        navigationMenuView.addItemDecoration(
            DividerItemDecoration(
                this@MainActivity,
                DividerItemDecoration.VERTICAL
            )
        )

        setMenuListeners(navigationView.menu, drawer!!)
        drawer!!.addDrawerListener(drawerListener)
        drawerToggle = ActionBarDrawerToggle(
            this, drawer, toolbar,
            R.string.open_drawer_description, R.string.close_drawer_description
        )
        drawer!!.addDrawerListener(drawerToggle!!)
        if (User.State.NOT_LOGGED == Settings.get().userState) {
            mainViewModelKt!!.getDrawerRegistrationBanner(this)
        }
        drawerToggle?.syncState()

        observeViewModel()

        mainViewModel!!.getInitialViewerData()
        mainViewModelKt!!.getUnvisitedCount()
        mainViewModelKt!!.getBonusRefund()
        loyaltyPointsSubscribe()
        loyaltyProgressSubscribe()
        loyaltyStatusSubscribe()
        loyaltyXOnPointsSubscribe()
        realTimeMessagesSubscribe()
        messagesSubscribe()
        onlineUsersSubscribe()
        promotionsCountSubscribe()
        tournamentsCountSubscribe()
        lotteriesCountSubscribe()

        setupInsets()
        setToolbarOnClickListeners()
        registerFacebookLoginListener()
        registerRequestPermissionNotificationLauncher()
    }

    override fun onStart() {
        super.onStart()
        //todo remove multiple subscription
        balanceSubscribe()
    }

    override fun onStop() {
        super.onStop()
    }

    private fun setupInsets() {
        ViewCompat.setOnApplyWindowInsetsListener(toolbar!!) { view: View?, insets: WindowInsetsCompat ->
            val insetTop = insets.systemWindowInsetTop
            val toolbarLp = toolbar!!.layoutParams
            if (toolbarLp is MarginLayoutParams) {
                toolbarLp.topMargin = insetTop
            }

            systemWindowInsetTop = insets.systemWindowInsetTop
            setContainerProfileInsets()

            val flDrawerHeaderNoUser =
                navHeader!!.findViewById<View>(R.id.fl_drawer_header_no_user)
            flDrawerHeaderNoUser.setPadding(
                flDrawerHeaderNoUser.paddingLeft, insets.systemWindowInsetTop,
                flDrawerHeaderNoUser.paddingRight, flDrawerHeaderNoUser.paddingBottom
            )

            binding!!.navigationMenu.setPadding(
                binding!!.navigationMenu.paddingLeft,
                binding!!.navigationMenu.paddingTop, binding!!.navigationMenu.paddingRight,
                insets.systemWindowInsetBottom
            )

            binding!!.navigationMenu.setNavBarShow(insets.systemWindowInsetBottom > 0)
            binding!!.navFooterView.root.layoutParams.height =
                if (insets.systemWindowInsetBottom > 0) resources.getDimensionPixelSize(R.dimen.navigation_menu_footer_height_with_navbar) else resources.getDimensionPixelSize(
                    R.dimen.navigation_menu_footer_height_without_navbar
                )

            binding!!.navFooterView.root.setPadding(
                binding!!.navFooterView.root.paddingLeft,
                binding!!.navFooterView.root.paddingTop, binding!!.navFooterView.root.paddingRight,
                insets.systemWindowInsetBottom
            )
            insets
        }

        ViewCompat.setOnApplyWindowInsetsListener(containerBottomBtn!!) { v: View?, insets: WindowInsetsCompat ->
            val insetBottom = insets.systemWindowInsetBottom
            val maxHeightNavigationBar =
                resources.getDimensionPixelSize(R.dimen.max_height_navigation_bar)
            if (insetBottom > maxHeightNavigationBar && !containerBottomBtn!!.hasFocus()) {
                containerBottomBtn!!.visibility = View.INVISIBLE
            } else if (isFastPay && screensWithBottomBtn.contains(
                    navController!!.currentDestination!!.id
                )
            ) {
                containerBottomBtn!!.visibility = View.VISIBLE
                btnBottomBalance!!.visibility = View.GONE
                btnBottom!!.visibility = View.GONE
                fastClickPaymentViewBottom!!.visibility = View.VISIBLE
            } else if (navController!!.currentDestination != null &&
                screensWithBottomBtn.contains(navController!!.currentDestination!!.id)
            ) {
                containerBottomBtn!!.visibility = View.VISIBLE
            }

            containerBottomBtn!!.setPadding(
                containerBottomBtn!!.paddingLeft,
                containerBottomBtn!!.paddingTop,
                containerBottomBtn!!.paddingRight, insetBottom
            )
            insets
        }

        ViewCompat.setOnApplyWindowInsetsListener(containerSystemMessages!!) { v: View?, insets: WindowInsetsCompat ->
            containerSystemMessages!!.setPadding(
                containerSystemMessages!!.paddingLeft,
                containerSystemMessages!!.paddingTop, containerSystemMessages!!.paddingRight,
                insets.systemWindowInsetBottom
            )
            insets
        }
    }

    private fun setOrientation(destinationId: Int) {
        val requestedOrientation = if (R.id.nav_game == destinationId) {
            ActivityInfo.SCREEN_ORIENTATION_FULL_SENSOR
        } else {
            ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        }
        <EMAIL> = requestedOrientation
    }

    private fun setContainerProfileInsets() {
        val containerProfile = navHeader!!.findViewById<View>(R.id.container_profile)
        containerProfile.setPadding(
            containerProfile.paddingLeft, systemWindowInsetTop,
            containerProfile.paddingRight, containerProfile.paddingBottom
        )
    }

    private fun observeViewModel() {
        mainViewModel!!.logoutResponseLiveData.observe(this, logoutObserver)
        mainViewModel!!.initialViewerDataQueryLiveData.observe(this, initialViewerDataObserver)
        mainViewModelKt!!.getBalanceLiveData().observe(this, balanceSubscriptionObserver)
        mainViewModelKt!!.getLoyaltyPointsLiveData()
            .observe(this, loyaltyPointsSubscriptionObserver)
        mainViewModelKt!!.getLoyaltyProgressLiveData()
            .observe(this, loyaltyProgressSubscriptionObserver)
        mainViewModelKt!!.getLoyaltyStatusLiveData()
            .observe(this, loyaltyStatusSubscriptionObserver)
        mainViewModelKt!!.getLoyaltyXOnPointsLiveData()
            .observe(this, loyaltyXOnPointsSubscriptionObserver)
        mainViewModelKt!!.getRealTimeNotificationLiveData()
            .observe(this, realTimeNotificationSubscriptionObserver)
        mainViewModelKt!!.getMessagesLiveData().observe(this, messagesSubscriptionObserver)
        mainViewModel!!.viewerEmptyLiveData.observe(this, viewerEmptyObserver)
        mainViewModel!!.makeRebillLiveData.observe(this, makeRebillObserver)
        mainViewModel!!.fastPaymentUrlLiveData.observe(this, fastPaymentUrlObserver)
        mainViewModel!!.serverErrorLiveData.observe(this, serverErrorObserver)
        mainViewModel!!.apolloExceptionLiveData.observe(this, apolloExceptionObserver)
        mainViewModel!!.fastPaymentRebillingLiveData.observe(this, fastPaymentRebillingObserver)
        mainViewModel!!.fastClickPaymentSystemLiveData.observe(this, fastClickPaymentSystemObserver)
        mainViewModel!!.checkPasswordResetTokenLiveData.observe(this, checkResetTokenObserver)
        mainViewModelKt!!.getTournamentLiveData().observe(this, tournamentObserver)
        mainViewModelKt!!.getSocialAuthLiveData().observe(this, socialAuthObserver)
        mainViewModelKt!!.apiAccessLiveData.observe(this, apiAccessObserver)
        mainViewModelKt!!.supportPhoneLiveData.observe(this, supportPhoneObserver)
        mainViewModelKt!!.xOnPointsLiveData.observe(this, xOnPointsObserver)
        mainViewModelKt!!.unvisitedCountLiveData.observe(this, unvisitedCountObserver)
        mainViewModelKt!!.promotionsCountLiveData.observe(this, promotionsCountObserver)
        mainViewModelKt!!.tournamentsCountLiveData.observe(this, tournamentCountObserver)
        mainViewModelKt!!.lotteriesCountLiveData.observe(this, lotteriesCountObserver)
        mainViewModelKt!!.bonusRefundLiveData.observe(this, bonusRefundObserver)
        mainViewModelKt!!.bonusResetBalanceLiveData.observe(this, bonusResetObserver)
    }

    private fun consumeLogoutEvent() {
        mainViewModel!!.consumeLogoutEvent()
    }

    private fun setToolbarOnClickListeners() {
        binding!!.appBar.ibToolbarWheelOfFortune.setOnClickListener { v: View? ->
            openScreenWithoutPopUpToHome(
                R.id.nav_wheel_of_fortune
            )
        }
        binding!!.appBar.ibToolbarSound.setOnClickListener { v: View? ->
            val currentFragment =
                currentFragment
            if (currentFragment is WheelOfFortuneFragment) {
                val isSoundEnable = currentFragment.isSoundEnable()
                if (isSoundEnable) {
                    binding!!.appBar.ibToolbarSound.setImageResource(R.drawable.ic_sound_off)
                } else {
                    binding!!.appBar.ibToolbarSound.setImageResource(R.drawable.ic_sound_on)
                }
                currentFragment.setSoundEnable(!isSoundEnable)
            }
        }
        binding!!.appBar.ibToolbarSearch.setOnClickListener { v: View? ->
            if (!isToolbarBtnsClickable) {
                return@setOnClickListener
            }
            val navOptions = NavOptions.Builder()
                .setEnterAnim(R.anim.slide_in_right)
                .setExitAnim(R.anim.slide_out_left)
                .setPopEnterAnim(R.anim.slide_in_left)
                .setPopExitAnim(R.anim.slide_out_right)
                .build()
            navController!!.navigate(R.id.nav_game_search, null, navOptions)
        }
        binding!!.appBar.ibToolbarProfile.setOnClickListener { v: View? ->
            if (!isToolbarBtnsClickable) {
                return@setOnClickListener
            }
            openProfileScreen()
        }
        binding!!.appBar.ivToolbarLogo.setOnClickListener { v: View? ->
            GeneralTools.hideKeyboard(this)
            if (!isToolbarBtnsClickable) {
                return@setOnClickListener
            }
            openHomeScreen()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == LinkFragment.REQUEST_CODE) {
            if (resultCode == RESULT_OK) {
                mainViewModel!!.getInitialViewerData()
                updateBonuses()
            }
        } else if (requestCode == YandexNetwork.REQUEST_CODE_YA_LOGIN) {
            try {
                val yandexAuthSdk = YandexNetwork().getYandexSdk(this@MainActivity)
                val yandexAuthToken = yandexAuthSdk.extractToken(resultCode, data)
                if (yandexAuthToken != null) {
                    sendSocialTokenToL4P(yandexAuthToken.value, SocialNetwork.YANDEX)
                }
            } catch (e: YandexAuthException) {
                showMessage(e.message!!)
            }
            return
        } else if (requestCode == TelegramNetwork.REQUEST_CODE_TELEGRAM_LOGIN) {
            if (resultCode == RESULT_OK) {
                //TODO telegram auth successful
            } else if (resultCode == TelegramPassport.RESULT_ERROR) {
                if (data != null) {
                    Log.e("Telegram", "" + data.getStringExtra("error"))
                }
            }
        }

        onActivityResult(requestCode, resultCode, data, vkAuthCallback)
        callbackManager.onActivityResult(requestCode, resultCode, data)
    }

    private val vkAuthCallback: VKAuthCallback = object : VKAuthCallback {
        override fun onLogin(vkAccessToken: VKAccessToken) {
            sendSocialTokenToL4P(vkAccessToken.accessToken, SocialNetwork.VKONTAKTE)
        }

        override fun onLoginFailed(i: Int) {
            Toast.makeText(
                this@MainActivity, getString(R.string.something_goes_wrong),
                Toast.LENGTH_LONG
            ).show()
        }
    }

    private val logoutObserver =
        Observer<Boolean> { isSuccess: Boolean? ->
            if (isSuccess == null) return@Observer
            if (isSuccess) {
                loyaltyPoints = 0.0
                loyaltyStatusId = 0
                loyaltyProgressPercent = 0

                clearPostponedLoyaltyStatus()
                firebaseAnalytics!!.setUserId("")
                applyNotLoggedState()
                if (navController!!.currentDestination != null &&
                    navController!!.currentDestination!!.id == R.id.nav_home
                ) {
                    applyNotLoggedStateHomeFragment()
                } else {
                    openScreen(R.id.nav_home)
                }

                containerSystemMessages!!.removeAllViews()
                mainViewModelKt!!.getDrawerRegistrationBanner(this)
                resetDrawerCounters()

                mainViewModelKt!!.resetBalanceLiveData()
                Settings.get().currentLoyaltyStatus = null

                consumeLogoutEvent()
            }
        }

    private val initialViewerDataObserver: Observer<GetInitialViewerDataQuery.Viewer> =
        object : Observer<GetInitialViewerDataQuery.Viewer> {
            override fun onChanged(viewer: GetInitialViewerDataQuery.Viewer) {
                val wallet = viewer.wallet
                if (wallet != null) {
                    if (wallet.currency != null) {
                        currencyCode = wallet.currency.code
                        Settings.get().userCurrencyCode = wallet.currency.code

                        val currencySymbol = wallet.currency.symbol
                        Settings.get().userCurrencySymbol = currencySymbol
                    }
                    <EMAIL> = wallet.balance.toString().toDouble()
                    <EMAIL> = wallet.realBalance.toString().toDouble()
                    setDrawerBalance(
                        "initialViewerDataObserver",
                        <EMAIL>, wallet.realBalance, wallet.bonusBalance
                    )
                    setBottomBtnBalance(<EMAIL>)
                    setPromotionScreenBalanceUpdated()

                    val viewerBonusBalance = wallet.bonusBalance
                    val bonusBalance =
                        if (viewerBonusBalance != null) realBalance.toString().toDouble() else 0.0
                    val userBalance = UserBalance(
                        <EMAIL>,
                        <EMAIL>, bonusBalance
                    )
                    mainViewModel!!.userBalance.postValue(userBalance)
                }

                val loyaltyProgress = viewer.loyaltyProgress
                if (loyaltyProgress != null) {
                    processLoyaltyStatus(loyaltyProgress)
                    setDrawerLoyalty(loyaltyProgress)
                }

                val profile = viewer.profile
                if (profile != null) {
                    tvUserNameDrawer!!.text = profile.userName
                }

                val fastClickPaymentSystem =
                    viewer.fastClickPaymentSystem
                if (fastClickPaymentSystem != null) {
                    isFastPay = true
                    btnReplenishBalance!!.visibility = View.GONE
                    fastClickPaymentView!!.visibility = View.VISIBLE
                    fastClickPaymentViewBottom!!.visibility = View.VISIBLE
                    btnBottomBalance!!.visibility = View.GONE
                    btnBottom!!.visibility = View.GONE
                    tvDrawerOtherMethods!!.visibility = View.VISIBLE
                    <EMAIL> =
                        FastClickPaymentSystem(fastClickPaymentSystem)
                    Settings.get()
                        .fastClickPaymentSystem = FastClickPaymentSystem(fastClickPaymentSystem)
                    fastClickPaymentView!!.setFastClickPaymentSystem(<EMAIL>)
                    fastClickPaymentViewBottom!!.setFastClickPaymentSystem(<EMAIL>)
                    updatePromotionScreenFastClickPaymentSystem(<EMAIL>!!)
                } else {
                    btnReplenishBalance!!.visibility = View.VISIBLE
                    fastClickPaymentView!!.visibility = View.GONE
                    fastClickPaymentViewBottom!!.visibility = View.GONE
                    tvDrawerOtherMethods!!.visibility = View.GONE
                    <EMAIL> = null
                    Settings.get().fastClickPaymentSystem = null
                }

                setCashback(viewer.cashbacks)
                if (viewer.messages != null) {
                    setMessagesCounter(viewer.messages.unreadCount)
                }
            }
        }

    private val buttonFastPaymentClickListener =
        FastClickPaymentView.ButtonFastPaymentClickListener { isRebill, amount ->
            if (isRebill) {
                mainViewModel!!.makeRebill(amount)
            } else {
                mainViewModel!!.getFastPaymentUrl(amount)
            }
        }

    private val buttonFastPaymentBottomClickListener =
        FastClickPaymentViewBottom.ButtonFastPaymentClickListener { isRebill, amount ->
            if (isRebill) {
                mainViewModel!!.makeRebill(amount)
            } else {
                mainViewModel!!.getFastPaymentUrl(amount)
            }
        }

    private val balanceSubscriptionObserver =
        Observer<SubscribeBalanceSubscription.Data?> { data: SubscribeBalanceSubscription.Data? ->
            if (data?.viewerWallet != null) {
                setDrawerBalance(
                    "balanceSubscriptionObserver", data.viewerWallet.balance,
                    data.viewerWallet.realBalance, data.viewerWallet.bonusBalance
                )
                val balance = data.viewerWallet.balance
                if (balance != null) {
                    <EMAIL> = balance.toString().toDouble()
                    setBottomBtnBalance(<EMAIL>)
                    setPromotionScreenBalanceUpdated()
                    setShopScreenBalance(<EMAIL>)
                }

                val realBalance = data.viewerWallet.realBalance
                if (realBalance != null) {
                    <EMAIL> = realBalance.toString().toDouble()
                }

                val viewerBonusBalance = data.viewerWallet.bonusBalance
                val bonusBalance = viewerBonusBalance?.toString()?.toDouble() ?: 0.0
                val userBalance = UserBalance(
                    <EMAIL>,
                    <EMAIL>, bonusBalance
                )
                mainViewModel!!.userBalance.postValue(userBalance)
            }
        }

    private val loyaltyPointsSubscriptionObserver =
        Observer<SubscribeLoyaltyPointsSubscription.Data?> { data: SubscribeLoyaltyPointsSubscription.Data? ->
            if (data?.viewerLoyaltyPoints != null) {
                <EMAIL> = data.viewerLoyaltyPoints.current
                setDrawerLoyaltyPoints(<EMAIL>)
                setExchangePointsLoyaltyPoints(loyaltyPoints)
                setShopLoyaltyPoints(loyaltyPoints)
            }
        }

    private val loyaltyProgressSubscriptionObserver =
        Observer<SubscribeLoyaltyProgressSubscription.Data?> { data: SubscribeLoyaltyProgressSubscription.Data? ->
            if (data?.viewerLoyaltyPercent != null) {
                <EMAIL> = data.viewerLoyaltyPercent.toInt()
                setDrawerLoyaltyProgress(loyaltyProgressPercent)

                val currentFragment = currentFragment
                if (currentFragment is LoyaltyProgramFragment) {
                    currentFragment.updateProgress(loyaltyProgressPercent)
                }
            }
        }

    private val loyaltyStatusSubscriptionObserver =
        Observer<SubscribeLoyaltyStatusSubscription.Data?> { data: SubscribeLoyaltyStatusSubscription.Data? ->
            if (data == null) {
                return@Observer
            }
            val loyaltyStatus = data.viewerLoyaltyStatus
            if (loyaltyStatus != null) {
                loyaltyStatusId = loyaltyStatus.id
                setDrawerStatus(loyaltyStatus.title, loyaltyStatusId)

                val currentFragment = currentFragment
                if (currentFragment is LoyaltyProgramFragment) {
                    currentFragment.updateLoyaltyStatusId(loyaltyStatusId)
                }

                processLoyaltyStatus(loyaltyStatus)
            }
        }

    private val loyaltyXOnPointsSubscriptionObserver =
        Observer<SubscribeLoyaltyXOnPointsSubscription.Data?> { data: SubscribeLoyaltyXOnPointsSubscription.Data? ->
            if (data?.viewerLoyaltyXOnPoints != null) {
                setDrawerLoyaltyXOnPoints(data.viewerLoyaltyXOnPoints.fragments.fragmentXOnPoints)
            }
        }

    private val realTimeNotificationSubscriptionObserver =
        Observer<SubscribeRealTimeNotificationSubscription.Data?> { data: SubscribeRealTimeNotificationSubscription.Data? ->
            if (data == null) {
                return@Observer
            }
            val viewerRealTimeNotification = data.viewerRealTimeNotification
            if (viewerRealTimeNotification != null && !TextUtils.isEmpty(viewerRealTimeNotification.content)) {
                showSystemMessage(
                    viewerRealTimeNotification.title,
                    viewerRealTimeNotification.content
                )
                mainViewModelKt!!.consumeRealTimeNotification()
            }
        }

    private val makeRebillObserver =
        Observer<Resource<String, ServerError, ApolloException>> { responseResource: Resource<String?, ServerError, ApolloException?>? ->
            if (responseResource == null) {
                return@Observer
            }
            when (responseResource.status) {
                Status.SUCCESS -> Log.d(
                    "FastClickPayment",
                    responseResource.data!!
                )

                Status.ERROR -> {
                    Toast.makeText(this, responseResource.error!!.errorMessage, Toast.LENGTH_SHORT)
                        .show()
                    fastClickPaymentView!!.hideLoader()
                    fastClickPaymentViewBottom!!.hideLoader()
                    fastClickPaymentViewBottom!!.showResult(false)
                    drawer!!.closeDrawers()
                }

                Status.FAILURE -> {
                    fastClickPaymentView!!.hideLoader()
                    drawer!!.closeDrawers()
                    showConnectionIssueMessage(responseResource.failure)
                }

                Status.VIEWER_EMPTY -> {
                    fastClickPaymentView!!.hideLoader()
                    fastClickPaymentViewBottom!!.hideLoader()
                    fastClickPaymentViewBottom!!.showResult(false)
                    drawer!!.closeDrawers()
                }
            }
        }

    private val messagesSubscriptionObserver =
        Observer<SubscribeMessagesSubscription.Data?> { data: SubscribeMessagesSubscription.Data? ->
            if (data == null) {
                return@Observer
            }
            val viewerMessage = data.viewerMessagesV2
            if (viewerMessage != null) {
                menuAdapter!!.increaseMessagesCounter()
                if (headerMessagesCounter!!.visibility != View.VISIBLE) {
                    headerMessagesCounter!!.visibility = View.VISIBLE
                }
                tvHeaderMessagesCounter!!.text = menuAdapter!!.messagesCount.toString()

                val formattedDate = DateTools.getFormattedDate(
                    this, viewerMessage.date,
                    DateTools.FormatDateType.MESSAGES
                )
                val messageItem = MessageItem(viewerMessage, formattedDate)
                val currentFragment = currentFragment
                if (currentFragment is MessagesFragment) {
                    currentFragment.addMessageItem(messageItem)
                }
            }
        }

    private val fastPaymentRebillingObserver: Observer<Boolean> = object : Observer<Boolean> {
        override fun onChanged(isRebilling: Boolean) {
            if (isRebilling) {
                fastClickPaymentView!!.showLoader()
                fastClickPaymentViewBottom!!.showLoader()
            } else {
                fastClickPaymentView!!.showSuccessfulRebillingMessage()
                fastClickPaymentViewBottom!!.showResult(true)
            }
        }
    }

    private val supportPhoneObserver =
        Observer<SupportPhone?> { supportPhone: SupportPhone? ->
            val text: String
            if (supportPhone == null) {
                text = ""
            } else if (supportPhone.getNumber().isEmpty()) {
                text = getString(R.string.support_phone_not_available)
                binding!!.navFooterView.tvDrawerSupportPhone.textSize = 12f
                binding!!.navFooterView.tvDrawerSupportPhone.setTextColor(
                    ContextCompat.getColor(this, R.color.black_50)
                )
            } else {
                text = supportPhone.getNumber()
                binding!!.navFooterView.tvDrawerSupportPhone.textSize = 24f
                binding!!.navFooterView.tvDrawerSupportPhone.setTextColor(
                    ContextCompat.getColor(this, R.color.black)
                )
            }
            binding!!.navFooterView.tvDrawerSupportPhone.text = text
        }

    private val xOnPointsObserver =
        Observer { xOnPointsEvent: XOnPointsEvent ->
            if (xOnPointsEvent.isFinish) {
                llDrawerXOnPointsInfo!!.visibility = View.GONE
            } else {
                tvXOnPointsToEnd!!.text = xOnPointsEvent.timeToEnd
            }
        }

    private val fastClickPaymentSystemObserver =
        Observer<GetFastClickPaymentSystemQuery.FastClickPaymentSystem> { fastClickPaymentSystem: GetFastClickPaymentSystemQuery.FastClickPaymentSystem? ->
            if (fastClickPaymentSystem != null) {
                isFastPay = true
                if (fastClickPaymentView!!.visibility != View.VISIBLE) {
                    btnReplenishBalance!!.visibility = View.GONE
                    fastClickPaymentView!!.visibility = View.VISIBLE
                    tvDrawerOtherMethods!!.visibility = View.VISIBLE
                }
                if (fastClickPaymentViewBottom!!.visibility != View.VISIBLE) {
                    btnReplenishBalance!!.visibility = View.GONE
                    fastClickPaymentViewBottom!!.visibility = View.VISIBLE
                    tvDrawerOtherMethods!!.visibility = View.VISIBLE
                }
                <EMAIL> =
                    FastClickPaymentSystem(fastClickPaymentSystem)
                Settings.get()
                    .fastClickPaymentSystem = FastClickPaymentSystem(fastClickPaymentSystem)
                fastClickPaymentView!!.setFastClickPaymentSystem(<EMAIL>)
                fastClickPaymentViewBottom!!.setFastClickPaymentSystem(<EMAIL>)
                updatePromotionScreenFastClickPaymentSystem(<EMAIL>!!)
            } else {
                btnReplenishBalance!!.visibility = View.VISIBLE
                fastClickPaymentView!!.visibility = View.GONE
                containerBottomBtn!!.visibility = View.VISIBLE
                fastClickPaymentViewBottom!!.visibility = View.GONE
                tvDrawerOtherMethods!!.visibility = View.GONE
                <EMAIL> = null
                Settings.get().fastClickPaymentSystem = null
            }
        }

    private val fastPaymentUrlObserver =
        Observer<String> { url: String? ->
            drawer!!.closeDrawers()
            openPaymentsScreen(PaymentsFragment.Screen.DEPOSIT, url)
        }

    private val viewerEmptyObserver =
        Observer<Boolean> { isViewerEmpty: Boolean? ->
            applyNotLoggedState()
            applyNotLoggedStateHomeFragment()
        }

    private val serverErrorObserver =
        Observer<ServerError> { serverError: ServerError? ->
            if (serverError == null) {
                return@Observer
            }
            Toast.makeText(this, serverError.errorMessage, Toast.LENGTH_SHORT).show()
            if (serverError.errorCode == Constants.SERVER_UNAUTHORIZED) {
                applyNotLoggedState()
                applyNotLoggedStateHomeFragment()
            }
        }

    private val apolloExceptionObserver =
        Observer<ApolloException> { exception: ApolloException? ->
            showConnectionIssueMessage(exception)
        }

    private val checkResetTokenObserver =
        Observer<Resource<String, ServerError, ApolloException>> { responseResource: Resource<String?, ServerError, ApolloException?>? ->
            if (responseResource == null) {
                return@Observer
            }
            when (responseResource.status) {
                Status.SUCCESS -> openNewPasswordScreen(responseResource.data)
                Status.ERROR -> Toast.makeText(
                    this@MainActivity,
                    responseResource.error!!.errorMessage, Toast.LENGTH_SHORT
                ).show()

                Status.FAILURE -> showConnectionIssueMessage(responseResource.failure)
                Status.VIEWER_EMPTY -> {}
            }
        }

    private val socialAuthObserver =
        Observer<Resource<AuthResponse, ServerError, ApolloException>> { responseResource: Resource<AuthResponse, ServerError, ApolloException?>? ->
            if (responseResource == null) {
                return@Observer
            }
            when (responseResource.status) {
                Status.SUCCESS -> {
                    val authResponse = responseResource.data
                    if (authResponse != null) {
                        val currentFragment = currentFragment
                        if (currentFragment is EnterFragment) {
                            currentFragment.processAuthentication(
                                authResponse.userId,
                                authResponse.email, true
                            )
                        }

                        val loginDataJson = JsonDataGenerator().getLoginDataJson(
                            LoginType.SOCIAL.value,
                            responseResource.data.userId
                        )
                        sendAnalytics(
                            this@MainActivity, AnalyticsEvent.LOGIN_APP,
                            loginDataJson, null
                        )
                    }
                }

                Status.ERROR -> Toast.makeText(
                    this@MainActivity,
                    responseResource.error!!.errorMessage, Toast.LENGTH_SHORT
                ).show()

                Status.FAILURE -> showConnectionIssueMessage(responseResource.failure)

                else -> {}
            }
        }

    private val tournamentObserver =
        Observer<Resource<TournamentsItem?, ServerError?, ApolloException?>> { responseResource: Resource<TournamentsItem?, ServerError?, ApolloException?>? ->
            if (responseResource == null) {
                return@Observer
            }
            when (responseResource.status) {
                Status.SUCCESS -> {
                    val tournamentsItem = responseResource.data
                    if (tournamentsItem != null) {
                        tournamentsItem.itemType = TournamentsItem.ItemType.TOURNAMENT_CURRENT
                        showTournamentInternal(tournamentsItem, false)
                    }
                }

                Status.ERROR -> {
                    val serverError = responseResource.error
                    if (serverError != null) {
                        showMessage(serverError.errorMessage)
                    }
                }

                Status.FAILURE -> showConnectionIssueMessage(responseResource.failure)

                else -> {}
            }
        }

    private val apiAccessObserver =
        Observer { apiAvailable: Boolean ->
            if (apiAvailable) {
                cloudflareIsResolving = false
                noConnectionUpdate()
                dialogCloudflare!!.cancel()
            }
        }

    private val unvisitedCountObserver =
        Observer<Resource<UnvisitedCount?, ServerError, ApolloException>> { responseResource: Resource<UnvisitedCount?, ServerError?, ApolloException?> ->
            when (responseResource.status) {
                Status.SUCCESS -> {
                    val unvisitedCount = responseResource.data
                    if (unvisitedCount != null) {
                        val bonusesUnvisitedCount = unvisitedCount.bonusesUnvisitedCount ?: 0
                        val tournamentsUnvisitedCount =
                            unvisitedCount.tournamentsUnvisitedCount ?: 0
                        val lotteriesUnvisitedCount = unvisitedCount.lotteriesUnvisitedCount ?: 0
                        menuAdapter!!.setPromotionsCounter(bonusesUnvisitedCount)
                        menuAdapter!!.setTournamentsCounter(tournamentsUnvisitedCount)
                        menuAdapter!!.setLotteriesCounter(lotteriesUnvisitedCount)
                    }
                }

                Status.ERROR -> {
                    val serverError = responseResource.error
                    if (serverError != null) {
                        showMessage(serverError.errorMessage)
                    }
                }

                Status.FAILURE -> showConnectionIssueMessage(responseResource.failure)

                else -> {}
            }
        }

    private val promotionsCountObserver =
        Observer<Int?> { response: Int? ->
            if (response != null) {
                menuAdapter!!.setPromotionsCounter(response)
            }
        }

    private val tournamentCountObserver =
        Observer<Int?> { response: Int? ->
            if (response != null) {
                menuAdapter!!.setTournamentsCounter(response)
            }
        }

    private val lotteriesCountObserver =
        Observer<Int?> { response: Int? ->
            if (response != null) {
                menuAdapter!!.setLotteriesCounter(response)
            }
        }

    private val bonusRefundObserver =
        Observer<Resource<BonusRefund, ServerError, ApolloException>> { responseResource: Resource<BonusRefund?, ServerError?, ApolloException?> ->
            when (responseResource.status) {
                Status.SUCCESS -> bonusRefund =
                    responseResource.data

                Status.ERROR -> if (responseResource.error != null) {
                    Log.w(TAG, responseResource.error.errorMessage)
                }

                Status.FAILURE -> showConnectionIssueMessage(responseResource.failure)

                else -> {}
            }
        }

    private val bonusResetObserver =
        Observer<Resource<Boolean, ServerError, ApolloException>> { responseResource: Resource<Boolean?, ServerError?, ApolloException?> ->
            when (responseResource.status) {
                Status.SUCCESS -> hideBonusWagering()
                Status.ERROR -> {
                    val serverError = responseResource.error
                    if (serverError != null) {
                        showMessage(serverError.errorMessage)
                    }
                }

                Status.FAILURE -> showConnectionIssueMessage(responseResource.failure)

                else -> {}
            }
        }

    private fun setDrawerLoyalty(loyaltyProgress: GetInitialViewerDataQuery.LoyaltyProgress) {
        if (loyaltyProgress.status != null) {
            loyaltyStatusId = loyaltyProgress.status.id
            setDrawerStatus(loyaltyProgress.status.title, loyaltyStatusId)
        }
        if (loyaltyProgress.percent != null) {
            loyaltyProgressPercent = loyaltyProgress.percent.toInt()
            setDrawerLoyaltyProgress(loyaltyProgressPercent)
        }

        if (loyaltyProgress.points != null) {
            <EMAIL> = loyaltyProgress.points.current
            setDrawerLoyaltyPoints(<EMAIL>)
        }

        setDrawerLoyaltyXOnPoints(loyaltyProgress.xOnPoints!!.fragments.fragmentXOnPoints)
    }

    private fun setDrawerStatus(status: String, loyaltyStatusId: Int) {
        (navHeader!!.findViewById<View>(R.id.tv_drawer_loyalty_status) as TextView).text =
            status
        (navHeader!!.findViewById<View>(R.id.iv_loyalty_status) as ImageView)
            .setImageDrawable(GeneralTools.getLoyaltyStatusDrawable(this, loyaltyStatusId, true))
    }

    override fun onBackPressed() {
        if (drawer!!.isDrawerOpen(GravityCompat.START)) {
            drawer!!.close()
        } else {
            super.onBackPressed()
        }
    }

    private fun setDrawerLoyaltyProgress(progress: Int) {
        (navHeader!!.findViewById<View>(R.id.tv_loyalty_percent) as TextView).text = "$progress%"
        (navHeader!!.findViewById<View>(R.id.loyalty_progress) as ProgressBar).progress =
            progress
    }

    private fun setDrawerLoyaltyPoints(points: Double) {
        tvDrawerBalancePoints!!.text = GeneralTools.formatNumber(points.toInt().toString())
    }

    private fun setDrawerLoyaltyXOnPoints(xOnPoints: FragmentXOnPoints?) {
        if (xOnPoints?.enabled != null && xOnPoints.enabled) {
            val xOnPointsName = xOnPoints.name
            if (!TextUtils.isEmpty(xOnPointsName) && !TextUtils.isEmpty(xOnPoints.endDate)) {
                val xOnPointsNameValue = xOnPointsName!!.replace("[^0-9]".toRegex(), "")
                tvDrawerXOnPoints!!.visibility = View.VISIBLE
                tvDrawerXOnPoints!!.text = getString(R.string.x_on_points, xOnPointsNameValue)
                tvXOnPointsInfoDescription!!.text =
                    getString(R.string.x_on_points_info_description, xOnPointsNameValue)

                val endDate = DateTools.getServerDate(this@MainActivity, xOnPoints.endDate)
                val timer = Timer()
                timer.schedule(object : TimerTask() {
                    override fun run() {
                        <EMAIL> {
                            tvDrawerXOnPoints!!.visibility =
                                View.GONE
                        }
                    }
                }, endDate)

                mainViewModelKt!!.setXOnPointsTimeToEnd(endDate)

                tvDrawerXOnPoints!!.setOnClickListener { v: View? ->
                    llDrawerXOnPointsInfo!!.visibility =
                        View.VISIBLE
                }
            } else {
                tvDrawerXOnPoints!!.visibility = View.GONE
            }
        } else {
            tvDrawerXOnPoints!!.visibility = View.GONE
        }
    }

    private fun setExchangePointsLoyaltyPoints(loyaltyPoints: Double) {
        val currentFragment = currentFragment
        if (currentFragment is ExchangePointsFragment) {
            currentFragment.setLoyaltyPoints(loyaltyPoints.toInt())
        }
    }

    private fun setShopLoyaltyPoints(loyaltyPoints: Double) {
        val currentFragment = currentFragment
        if (currentFragment is ShopFragment) {
            currentFragment.setLoyaltyPoints(loyaltyPoints.toInt())
        }
    }

    private fun setCashback(cashbacks: GetInitialViewerDataQuery.Cashbacks?) {
        if (cashbacks != null) {
            var statusAmount = try {
                cashbacks.status!!.amount.toString().toDouble()
            } catch (e: NumberFormatException) {
                0.0
            } catch (e: NullPointerException) {
                0.0
            }
            var storeAmount = try {
                cashbacks.store!!.amount.toString().toDouble()
            } catch (e: NumberFormatException) {
                0.0
            } catch (e: NullPointerException) {
                0.0
            }
            val cashbackSum = statusAmount + storeAmount

            tvDrawerCashback!!.text = GeneralTools.formatBalance(currencyCode, cashbackSum)
        } else {
            tvDrawerCashback!!.text = GeneralTools.formatBalance(currencyCode, 0.0)
        }
    }

    fun showConnectionIssueMessage(exception: ApolloException?) {
        runOnUiThread {
            if (exception is ApolloNetworkException) {
                showNoInternetMessage()
            } else if (exception is ApolloHttpException &&
                (exception.statusCode == 403 || exception.statusCode >= 500)
            ) {
                showUnstableConnectionMessage()
            }
        }
    }

    fun showConnectionIssueMessage(exception: ResponseException) {
        runOnUiThread {
            if (ResponseException.ExceptionType.NETWORK == exception.exceptionType) {
                showNoInternetMessage()
            } else if (ResponseException.ExceptionType.HTTP == exception.exceptionType &&
                (exception.statusCode == 403 || exception.statusCode >= 500)
            ) {
                showUnstableConnectionMessage()
            }
        }
    }

    private fun showUnstableConnectionMessage() {
        if (navController!!.currentDestination != null &&
            navController!!.currentDestination!!.id != R.id.nav_no_connection
        ) {
            val bundle = Bundle()
            bundle.putString(
                NoConnectionFragment.KEY_TYPE,
                NoConnectionFragment.VALUE_UNSTABLE_CONNECTION
            )
            navController!!.navigate(R.id.nav_no_connection, bundle)
        }
    }

    fun showNoInternetMessage() {
        if (navController!!.currentDestination != null &&
            navController!!.currentDestination!!.id != R.id.nav_no_connection
        ) {
            val bundle = Bundle()
            bundle.putString(NoConnectionFragment.KEY_TYPE, NoConnectionFragment.VALUE_NO_INTERNET)
            navController!!.navigate(R.id.nav_no_connection, bundle)
        }
    }

    fun showMessage(text: String) {
        val message = Snackbar.make(
            binding!!.appBar.coordinatorLayoutRoot,
            text, Snackbar.LENGTH_INDEFINITE
        )

        val snackbarView = message.view
        val snackTextView =
            snackbarView.findViewById<TextView>(com.google.android.material.R.id.snackbar_text)
        snackTextView.maxLines = 4

        message.setAction(android.R.string.ok) { v: View? -> message.dismiss() }
        message.show()
    }

    fun showSystemMessage(title: String?, message: String) {
        val inflater = getSystemService(LAYOUT_INFLATER_SERVICE) as LayoutInflater
        val view = inflater.inflate(R.layout.item_system_message, null)

        val ivClose = view.findViewById<ImageView>(R.id.ib_close)
        ivClose.setOnClickListener { v: View? ->
            containerSystemMessages!!.removeView(
                view
            )
        }

        (view.findViewById<View>(R.id.tv_title) as TextView).text = title
        setHtmlSystemMessage(
            view.findViewById(R.id.wv_message), message,
            containerSystemMessages!!
        )

        val handler = Handler(Looper.getMainLooper())
        handler.postDelayed({ containerSystemMessages!!.removeView(view) }, 30000)

        containerSystemMessages!!.addView(view, containerSystemMessages!!.childCount)
    }

    fun setHtmlSystemMessage(
        wvMessage: WebView,
        htmlMessage: String,
        containerSystemMessages: LinearLayout
    ) {
        val CSS_ASSET_FILE_NAME = "systemMessage.css"
        wvMessage.setBackgroundColor(Color.TRANSPARENT)
        wvMessage.settings.javaScriptEnabled = true
        wvMessage.webViewClient = object : WebViewClient() {
            override fun shouldOverrideUrlLoading(
                view: WebView,
                url: String
            ): Boolean {
                processDeepLinkUrl(url, UrlSource.SUBSCRIPTION_MESSAGE)
                containerSystemMessages.removeAllViews()
                return true
            }

            @RequiresApi(api = Build.VERSION_CODES.KITKAT)
            override fun onPageFinished(view: WebView, url: String) {
                val css =
                    <EMAIL>(CSS_ASSET_FILE_NAME)
                val js =
                    "var style = document.createElement('style'); style.innerHTML = `$css`; document.head.appendChild(style);"
                wvMessage.evaluateJavascript(js, null)
                super.onPageFinished(view, url)
            }
        }

        wvMessage.loadDataWithBaseURL(ApoloConfig.BASE_URL, htmlMessage, "text/html", "UTF-8", "")
    }

    private fun setBalanceAndEmailPlaceholder() {
        tvUserNameDrawer!!.text = Settings.get().userName
        currencyCode = getString(R.string.default_currency_code)
        setDrawerBalance("setBalanceAndEmailPlaceholder", 0, 0, 0)
    }

    private fun applyNotLoggedStateHomeFragment() {
        val currentFragment = currentFragment
        if (currentFragment is HomeFragment) {
            currentFragment.applyNotLoggedState()
        }
    }

    private val drawerListener: DrawerListener = object : DrawerListener {
        override fun onDrawerSlide(drawerView: View, slideOffset: Float) {
        }

        override fun onDrawerOpened(drawerView: View) {
            tvUserNameDrawer!!.text = Settings.get().userName
            mainViewModelKt?.updateBonusBalancesCount()
        }

        override fun onDrawerClosed(drawerView: View) {
            fastClickPaymentView!!.hideKeyboard()
            fastClickPaymentViewBottom!!.hideKeyboard()
            llDrawerXOnPointsInfo!!.visibility = View.GONE
        }

        override fun onDrawerStateChanged(newState: Int) {
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        GeneralTools.hideKeyboard(this)
        val navController = Navigation.findNavController(this, R.id.nav_host_fragment)
        return NavigationUI.navigateUp(navController, mAppBarConfiguration!!)
                || super.onSupportNavigateUp()
    }

    override fun onSuccess(banners: List<GetDrawerBannerQuery.Banner>?) {
        runOnUiThread {
            if (lifecycle.currentState.isAtLeast(Lifecycle.State.RESUMED)) {
                if (!banners.isNullOrEmpty()) {
                    val image = banners[0].image
                    if (!TextUtils.isEmpty(image)) {
                        val fullUrl = ApoloConfig.getFullUrl(image)
                        Picasso.get().load(fullUrl)
                            .into(navHeader!!.findViewById<View>(R.id.iv_drawer_ad) as ImageView)
                    }
                }
            }
        }
    }

    override fun onFailure(e: ApolloException) {
        Log.e(TAG, "Failed to load: " + e.localizedMessage)
        runOnUiThread {
            if (lifecycle.currentState.isAtLeast(Lifecycle.State.RESUMED)) {
                Toast.makeText(this, e.localizedMessage, Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun processDeepLink() {
        val intent = intent
        val extras = intent.extras
        if (extras != null) {
            val deepLinkUrl = extras.getString(Constants.DEEP_LINK_URL)
            val openLoginScreen = extras.getBoolean(Constants.OPEN_LOGIN_SCREEN)
            val bonusAction = extras.getString(Constants.BONUS_ACTION)
            val bonusId = extras.getString(Constants.BONUS_ID)
            val userId = extras.getString(Constants.USER_ID)
            val hash = extras.getString(Constants.HASH)

            if (openLoginScreen) {
                showLoginScreen(deepLinkUrl)
            } else if (!TextUtils.isEmpty(deepLinkUrl)) {
                processDeepLinkUrl(deepLinkUrl!!, UrlSource.CRM_PUSH)
            }

            if (!TextUtils.isEmpty(bonusAction) && !TextUtils.isEmpty(bonusId) &&
                !TextUtils.isEmpty(userId) && !TextUtils.isEmpty(hash)
            ) {
                processDeepLinkBonus(
                    bonusAction,
                    bonusId!!,
                    userId!!,
                    hash!!,
                    TextUtils.isEmpty(deepLinkUrl)
                )
            }
        }
    }

    fun processDeepLinkUrl(deepLinkUrl: String?, urlSource: UrlSource) {
        if (deepLinkUrl == null) return

        if (MessengerHelper().detectMessengerLinkAndProcess(
                deepLinkUrl,
                packageManager,
                this@MainActivity,
                object : MessengerCallback {
                    override fun onMessengerError(error: MessengerError) {
                        if (error == MessengerError.VIBER_IS_NOT_INSTALLED) {
                            showMessage(getString(R.string.message_viber_is_not_installed))
                        }
                    }
                })
        ) {
            return
        }

        val userState = Settings.get().userState
        if (UrlSource.SLIDES == urlSource && User.State.PLAYER != userState) {
            openEnterScreen(false)
            return
        }

        try {
            val aURL = if (deepLinkUrl.contains("://")) URL(deepLinkUrl) else URL(
                "http://$deepLinkUrl"
            )

            val ref = aURL.ref
            val path = aURL.path

            if (!TextUtils.isEmpty(ref)) {
                if (User.State.PLAYER == userState) {
                    when (ref) {
                        Constants.DEEP_LINK_PAYMENTS_REF -> openPaymentsScreen(PaymentsFragment.Screen.DEPOSIT)
                        Constants.DEEP_LINK_PAYMENTS_HISTORY_REF -> openPaymentsScreen(
                            PaymentsFragment.Screen.HISTORY
                        )

                        Constants.DEEP_LINK_PROFILE_REF -> openProfileScreen()
                        Constants.DEEP_LINK_LOYALTY_EXCHANGE_REF -> openExchangePointsScreen()
                        Constants.DEEP_LINK_NEW_PASS_REF -> mainViewModel!!.checkPasswordResetToken(
                            path.split("/".toRegex()).dropLastWhile { it.isEmpty() }
                                .toTypedArray()[2])

                        Constants.DEEP_LINK_MESSAGEBOX_REF -> {}
                        Constants.DEEP_LINK_RESULTS_REF -> if (path.contains(Constants.DEEP_LINK_TOURNAMENTS)) {
                            val pathArr = path.split("/".toRegex()).dropLastWhile { it.isEmpty() }
                                .toTypedArray()
                            val tournamentId = pathArr[pathArr.size - 1]
                            showTournamentInternal(tournamentId.toInt())
                        } else if (path.contains(Constants.DEEP_LINK_LOTTERIES)) {
                            val pathArr = path.split("/".toRegex()).dropLastWhile { it.isEmpty() }
                                .toTypedArray()
                            val lotteryId = pathArr[pathArr.size - 1]
                            showLotteryInternal(lotteryId.toInt(), true)
                        }

                        Constants.DEEP_LINK_WHEEL_OF_FORTUNE_PAID_REF -> openScreen(R.id.nav_wheel_of_fortune)
                        Constants.DEEP_LINK_WHEEL_OF_FORTUNE_REF -> openScreen(R.id.nav_wheel_of_fortune)
                        else -> showUnknownLinkDialog(aURL.toString())
                    }
                } else if (User.State.NOT_LOGGED == userState) {
                    if (Constants.DEEP_LINK_LOGIN_REF == ref) {
                        showEnterScreen(true)
                    } else if (Constants.DEEP_LINK_REGISTER_REF == ref) {
                        showEnterScreen(false)
                    } else {
                        showUnknownLinkDialog(aURL.toString())
                    }
                }
            } else if (!TextUtils.isEmpty(path)) {
                if (path.contains(Constants.DEEP_LINK_BONUSES)) {
                    openPromotionsScreen(UrlSource.NEWS != urlSource)
                } else if (path.contains(Constants.DEEP_LINK_GAME_ROOM)) {
                    openScreen(R.id.nav_game_room)
                } else if (path.contains(Constants.DEEP_LINK_DEMO_GAME) || path.contains(Constants.DEEP_LINK_GAME)) {
                    when (urlSource) {
                        UrlSource.CRM_PUSH -> {
                            val gameType =
                                if (path.contains(Constants.DEEP_LINK_DEMO_GAME)) PregameFragment.GAME_TYPE_DEMO else PregameFragment.GAME_TYPE_FULL
                            openGameWithPregame(deepLinkUrl, gameType)
                        }

                        UrlSource.BONUS, UrlSource.SUBSCRIPTION_MESSAGE, UrlSource.SLIDES -> openPregame(
                            deepLinkUrl
                        )

                        UrlSource.NEWS -> if (User.State.PLAYER == Settings.get().userState) {
                            openPregame(deepLinkUrl)
                        } else {
                            openEnterScreen(false)
                        }
                    }
                } else if (path.contains(Constants.DEEP_LINK_TERMS)) {
                    openRulesScreen()
                } else if (path.contains(Constants.DEEP_LINK_LOYALTY_PROGRAM)) {
                    openScreen(R.id.nav_loyalty_program)
                } else if (path.contains(Constants.DEEP_LINK_GAME_POPULAR)) {
                    selectHomeTab(GameTab.BEST)
                } else if (path.contains(Constants.DEEP_LINK_GAME_NEW)) {
                    selectHomeTab(GameTab.NEW)
                } else if (path.contains(Constants.DEEP_LINK_GAME_SLOTS)) {
                    selectHomeTab(GameTab.SLOTS)
                } else if (path.contains(Constants.DEEP_LINK_GAME_FAVOURITES)) {
                    selectHomeTab(GameTab.FAVOURITES)
                } else if (path.contains(Constants.DEEP_LINK_GAME_TABLES)) {
                    selectHomeTab(GameTab.TABLE)
                } else if (path.contains(Constants.DEEP_LINK_NEWS)) {
                    val pathArr =
                        path.substring(1).split("/".toRegex()).dropLastWhile { it.isEmpty() }
                            .toTypedArray()
                    if (pathArr.size == 1) {
                        openScreen(R.id.nav_news)
                    } else if (pathArr.size == 2) {
                        openNewsInternalScreen(pathArr[1])
                    }
                } else if (path.contains(Constants.DEEP_LINK_BONUS_ACTIVATE) ||
                    path.contains(Constants.DEEP_LINK_BONUS_RECEIVE)
                ) {
                    openPromotionsScreen(true)
                } else if (path.contains(Constants.DEEP_LINK_EMAIL_CONFIRMATION)) {
                    mainViewModel!!.confirmEmail(
                        path.split("/".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()[3]
                    )
                } else if (path.contains(Constants.DEEP_LINK_TOURNAMENTS)) {
                    openScreen(R.id.nav_tournaments)
                } else if (path.contains(Constants.DEEP_LINK_CASHBACK)) {
                    openScreen(R.id.nav_cashback)
                } else if (path != "/") {
                    showUnknownLinkDialog(aURL.toString())
                }
            }
        } catch (e: MalformedURLException) {
            Log.e(TAG, e.toString())
        } catch (e: NullPointerException) {
            Log.e(TAG, e.toString())
        }
    }

    private fun showUnknownLinkDialog(url: String) {
        val dialog = Dialog(this)
        dialog.setContentView(R.layout.dialog_unknown_link)

        dialog.findViewById<View>(R.id.ib_close).setOnClickListener { v: View? -> dialog.cancel() }

        dialog.findViewById<View>(R.id.btn_support_chat).setOnClickListener { v: View? ->
            dialog.cancel()
            openScreen(R.id.nav_support_chat)
        }

        dialog.show()

        // FirebaseAnalytics log event unknown_url
        val params = Bundle()
        params.putString(FirebaseEvents.UnknownUrl.urlParam, url)
        firebaseAnalytics!!.logEvent(FirebaseEvents.UnknownUrl.name, params)
    }

    private fun processDeepLinkBonus(
        bonusAction: String?, bonusId: String, userId: String, hash: String,
        openBonusScreen: Boolean
    ) {
        if (Constants.BONUS_ACTION_RECEIVE == bonusAction) {
            bonusReceiveWithHash(bonusId, userId, hash, object : GenericTarget<Boolean> {
                override fun onSuccess(isSuccess: Boolean?) {
                    showMessage(if (isSuccess!!) getString(R.string.bonus_received) else getString(R.string.bonus_not_received))
                }

                override fun onError(
                    errorMessage: String,
                    errorCode: String,
                    fieldsErrors: FieldsErrorsHolder
                ) {
                    showMessage(errorMessage)
                }

                override fun onFailure(exception: ApolloException) {
                    if (lifecycle.currentState.isAtLeast(Lifecycle.State.RESUMED)) {
                        showConnectionIssueMessage(exception)
                    }
                }
            })
        } else if (Constants.BONUS_ACTION_ACTIVATE == bonusAction) {
            bonusActivateWithHash(bonusId, userId, hash, object : GenericTarget<Boolean> {
                override fun onSuccess(isSuccess: Boolean?) {
                    showMessage(
                        if (isSuccess == true) getString(R.string.bonus_activated) else getString(
                            R.string.bonus_not_activated
                        )
                    )
                }

                override fun onError(
                    errorMessage: String,
                    errorCode: String,
                    fieldsErrors: FieldsErrorsHolder
                ) {
                    showMessage(errorMessage)
                }

                override fun onFailure(exception: ApolloException) {
                    if (lifecycle.currentState.isAtLeast(Lifecycle.State.RESUMED)) {
                        showConnectionIssueMessage(exception)
                    }
                }
            })
        }

        if (openBonusScreen) {
            openPromotionsScreen(true)
        }
    }

    private fun processOpenIntentScreen() {
        val screen = intent.getSerializableExtra(KEY_SCREEN) as Screen?
        if (screen != null) {
            val fullGameUrl: String?
            when (screen) {
                Screen.DEPOSIT -> {
                    val depositUrl = intent.getStringExtra(DEPOSIT_URL)
                    fullGameUrl = intent.getStringExtra(FULL_GAME_URL)
                    openPaymentsScreen(PaymentsFragment.Screen.DEPOSIT, depositUrl, fullGameUrl)
                }

                Screen.REGISTER -> openEnterScreen(false)
                Screen.HOME -> openHomeScreen()
                Screen.SUPPORT_CHAT -> {
                    val gameUrl = intent.getStringExtra(GAME_URL)
                    fullGameUrl = intent.getStringExtra(FULL_GAME_URL)
                    openSupportChat(gameUrl, fullGameUrl)
                }

                Screen.PREGAME -> showPregame(SessionDataHolder.getInstance().currentGameItem, null)
                Screen.TOURNAMENT -> {
                    val tournamentId = intent.getIntExtra(TOURNAMENT_ID, 0)
                    showTournamentInternal(tournamentId)
                }

                else -> {}
            }
        }
    }

    private fun setDrawerBalance(
        source: String,
        balanceObject: Any,
        realBalanceObject: Any?,
        bonusBalanceObject: Any?
    ) {
        binding!!.root.post {
            try {
                val balance = balanceObject.toString().toDouble()
                val realBalance = realBalanceObject.toString().toDouble()
                val bonusBalance = bonusBalanceObject.toString().toDouble()
                tvBalanceDrawer!!.text =
                    GeneralTools.formatBalance(currencyCode, floor(balance))
                tvRealBalanceDrawer!!.text =
                    GeneralTools.formatBalance(currencyCode, floor(realBalance))
                tvBonusBalanceDrawer!!.text =
                    GeneralTools.formatBalance(currencyCode, floor(bonusBalance))
            } catch (e: NumberFormatException) {
                FirebaseCrashlytics.getInstance().log(
                    "setDrawerBalance() source: " + source +
                            " | currentBalance: " + <EMAIL>
                )
                FirebaseCrashlytics.getInstance().recordException(e)
            }
        }
    }

    private fun setBottomBtnBalance(balanceObject: Any) {
        val balance = balanceObject.toString().toDouble()
        val formattedBalance = GeneralTools.formatBalance(Settings.get().userCurrencyCode, balance)
        tvBottomBtnBalance!!.text = formattedBalance
    }

    private fun selectHomeTab(gameTab: GameTab) {
        binding!!.root.post {
            val currentFragment = currentFragment
            if (currentFragment is HomeFragment) {
                currentFragment.selectTab(gameTab)
            }
        }
    }

    private fun setPromotionScreenBalanceUpdated() {
        binding!!.root.post {
            val currentFragment = currentFragment
            if (currentFragment is PromotionsFragment) {
                currentFragment.balanceUpdated()
            }
        }
    }

    private fun setShopScreenBalance(balance: Double) {
        val currentFragment = currentFragment
        if (currentFragment is ShopFragment) {
            currentFragment.setBalance(balance)
        }
    }

    private fun updatePromotionScreenFastClickPaymentSystem(fastClickPaymentSystem: FastClickPaymentSystem) {
        binding!!.root.post {
            val currentFragment = currentFragment
            if (currentFragment is PromotionsFragment) {
                currentFragment.updateFastClickPaymentSystem(
                    fastClickPaymentSystem
                )
            }
        }
    }

    private fun updateBonuses() {
        binding!!.root.post {
            val currentFragment = currentFragment
            if (currentFragment is PromotionsFragment) {
                currentFragment.updateBonuses()
            }
        }
    }

    override fun applyLoggedState() {
        navHeader!!.findViewById<View>(R.id.cl_drawer_header_logged).visibility =
            View.VISIBLE
        navHeader!!.findViewById<View>(R.id.fl_drawer_header_no_user).visibility =
            View.GONE
        navHeader!!.findViewById<View>(R.id.iv_drawer_header_organic).visibility =
            View.GONE

        navHeader!!.findViewById<View>(R.id.cl_drawer_header_logged)
            .setOnClickListener { v: View? ->
                llDrawerXOnPointsInfo!!.visibility =
                    View.GONE
            }

        if (fastClickPaymentViewBottom!!.visibility == View.VISIBLE) {
            btnBottomBalance!!.visibility = View.GONE
            btnBottom!!.visibility = View.GONE
        } else {
            applyLoggedStateBottomBtn()
        }
        binding!!.navFooterView.root.visibility = View.VISIBLE
        menuAdapter!!.applyLoggedState()
        applyUserStateToToolbar(User.State.PLAYER)

        navHeader!!.findViewById<View>(R.id.container_profile).setOnClickListener { v: View? ->
            drawer!!.closeDrawers()
            openProfileScreen()
        }

        setContainerProfileInsets()
    }

    override fun applyNotLoggedState() {
        isFastPay = false
        navHeader!!.findViewById<View>(R.id.cl_drawer_header_logged).visibility =
            View.GONE
        navHeader!!.findViewById<View>(R.id.fl_drawer_header_no_user).visibility =
            View.VISIBLE
        navHeader!!.findViewById<View>(R.id.iv_drawer_header_organic).visibility =
            View.GONE
        fastClickPaymentViewBottom!!.visibility = View.GONE
        btnBottom!!.visibility = View.VISIBLE
        applyNotLoggedStateBottomBtn()
        binding!!.navFooterView.root.visibility = View.VISIBLE
        menuAdapter!!.applyNotLoggedState()
        applyUserStateToToolbar(User.State.NOT_LOGGED)
        mainViewModelKt!!.cancelAllSubscriptions()
    }

    override fun applyOrganicState() {
        navHeader!!.findViewById<View>(R.id.cl_drawer_header_logged).visibility =
            View.GONE
        navHeader!!.findViewById<View>(R.id.fl_drawer_header_no_user).visibility =
            View.GONE
        navHeader!!.findViewById<View>(R.id.iv_drawer_header_organic).visibility =
            View.VISIBLE

        binding!!.navFooterView.root.visibility = View.GONE
        menuAdapter!!.applyOrganicState()
        applyUserStateToToolbar(User.State.ORGANIC)
    }

    private fun applyLoggedStateBottomBtn() {
        btnBottomBalance!!.visibility = View.VISIBLE
        btnBottom!!.setText(R.string._replenish)
        containerBottomBtn!!.setOnClickListener { v: View? ->
            sendAnalytics(
                this, AnalyticsEvent.STICKY_CASHIER,
                null, null
            )
            openPaymentsScreen(PaymentsFragment.Screen.DEPOSIT)
        }
    }

    private fun applyNotLoggedStateBottomBtn() {
        btnBottomBalance!!.visibility = View.GONE
        btnBottom!!.setText(R.string._registration)
        containerBottomBtn!!.setOnClickListener { v: View? -> openEnterScreen(false) }
    }

    private fun applyUserStateToToolbar(state: User.State) {
        when (state) {
            User.State.PLAYER -> applyPlayerStateToToolbar()
            User.State.ORGANIC -> {
                binding!!.appBar.bToolbarButton.visibility = View.GONE
                binding!!.appBar.ibToolbarProfile.visibility = View.GONE
            }

            User.State.NOT_LOGGED -> applyNotLoggedStateToToolbar()
        }

        binding!!.appBar.bToolbarButton.setOnClickListener { _: View? ->
            try {
                if (navController!!.currentDestination!!.id == R.id.nav_enter) {
                    val fragment = supportFragmentManager
                        .findFragmentById(R.id.nav_host_fragment)
                        ?.getChildFragmentManager()?.fragments?.get(0) as? EnterFragment

                    fragment?.let {
                        fragment.isLogin = !fragment.isLogin
                        fragment.setupMode(fragment.isLogin, this@MainActivity, true)
                    }
                    binding!!.appBar.bToolbarButton.setText(if (fragment?.isLogin == true) R.string._registration else R.string._login)
                } else {
                    when (state) {
                        User.State.PLAYER -> {
                            openPaymentsScreen(PaymentsFragment.Screen.DEPOSIT)
                            return@setOnClickListener
                        }

                        User.State.NOT_LOGGED -> showEnterScreen(false)
                        else -> {}
                    }
                }
            } catch (e: NullPointerException) {
                //do nothing
            } catch (e: ClassCastException) {
                //do nothing
            }
        }
    }

    private fun applyPlayerStateToToolbar() {
        binding!!.appBar.bToolbarButton.visibility = View.GONE
        binding!!.appBar.ibToolbarProfile.visibility = View.VISIBLE

        val constraintSet = ConstraintSet()
        constraintSet.clone(binding!!.appBar.clToolbar)
        constraintSet.connect(
            R.id.ib_toolbar_search, ConstraintSet.RIGHT,
            R.id.ib_toolbar_profile, ConstraintSet.LEFT, 0
        )
        constraintSet.applyTo(binding!!.appBar.clToolbar)
    }

    private fun applyNotLoggedStateToToolbar() {
        binding!!.appBar.bToolbarButton.setText(R.string._registration)
        binding!!.appBar.bToolbarButton.visibility = View.VISIBLE
        binding!!.appBar.ibToolbarProfile.visibility = View.GONE
        binding!!.appBar.ibToolbarWheelOfFortune.visibility = View.GONE

        val constraintSet = ConstraintSet()
        constraintSet.clone(binding!!.appBar.clToolbar)
        constraintSet.connect(
            R.id.ib_toolbar_search, ConstraintSet.RIGHT,
            R.id.b_toolbar_button, ConstraintSet.LEFT, 0
        )
        constraintSet.applyTo(binding!!.appBar.clToolbar)
    }

    private fun updateToolbar(destinationId: Int) {
        if (destinationId == R.id.nav_profile || destinationId == R.id.nav_pregameFragment || destinationId == R.id.nav_payments || destinationId == R.id.nav_new_password || destinationId == R.id.nav_biometric_input_password) {
            binding!!.appBar.ibToolbarSearch.visibility = View.GONE
        } else {
            binding!!.appBar.ibToolbarSearch.visibility = View.VISIBLE
        }

        if (destinationId == R.id.nav_payments || destinationId == R.id.nav_pregameFragment || destinationId == R.id.nav_new_password || destinationId == R.id.nav_biometric_input_password) {
            binding!!.appBar.ibToolbarProfile.visibility = View.GONE
        } else if (User.State.PLAYER == Settings.get().userState) {
            binding!!.appBar.ibToolbarProfile.visibility = View.VISIBLE
        }

        if (destinationId == R.id.nav_wheel_of_fortune) {
            binding!!.appBar.ibToolbarWheelOfFortune.visibility = View.GONE
            binding!!.appBar.ibToolbarSound.visibility = View.VISIBLE
            binding!!.appBar.ibToolbarSound.setImageResource(R.drawable.ic_sound_on)
        } else {
            binding!!.appBar.ibToolbarSound.visibility = View.GONE
            if (User.State.PLAYER == Settings.get().userState) {
                binding!!.appBar.ibToolbarWheelOfFortune.visibility = View.VISIBLE
            } else {
                binding!!.appBar.ibToolbarWheelOfFortune.visibility = View.GONE
            }
        }
    }

    private fun applyBottomBtnVisibilityState(destinationId: Int) {
        if (screensWithBottomBtn.contains(destinationId) && !isFastPay) {
            fastClickPaymentViewBottom!!.visibility = View.GONE
        } else if (screensWithBottomBtn.contains(destinationId) && isFastPay) {
            fastClickPaymentViewBottom!!.visibility = View.VISIBLE
            containerBottomBtn!!.visibility = View.VISIBLE
            btnBottom!!.visibility = View.GONE
        } else {
            fastClickPaymentViewBottom!!.visibility = View.GONE
            containerBottomBtn!!.visibility = View.GONE
        }
    }

    private fun setMenuListeners(menu: Menu, drawer: DrawerLayout) {
        navHeader!!.findViewById<View>(R.id.btn_drawer_register).setOnClickListener { v: View? ->
            openEnterScreen(false)
            drawer.closeDrawers()
        }
        binding!!.navigationMenu.setOnGroupClickListener { expandableListView: ExpandableListView?, view: View?, position: Int, l: Long ->
            val selectedMenuItem = menuAdapter!!.getMenuItemByPosition(position)
            when (selectedMenuItem) {
                ExpandedMenuModel.MenuItem.HOME -> openScreen(R.id.nav_home)
                ExpandedMenuModel.MenuItem.PROMOTIONS -> openScreen(R.id.nav_promotions)
                ExpandedMenuModel.MenuItem.TOURNAMENTS -> openScreen(R.id.nav_tournaments)
                ExpandedMenuModel.MenuItem.GAME_ROOM -> openScreen(R.id.nav_game_room)
                ExpandedMenuModel.MenuItem.LOTTERY -> openScreen(R.id.nav_lottery_list)
                ExpandedMenuModel.MenuItem.SHOP -> openScreen(R.id.nav_shop)
                ExpandedMenuModel.MenuItem.CASH_BOX -> {}
                ExpandedMenuModel.MenuItem.WHEEL_OF_FORTUNE -> openScreen(R.id.nav_wheel_of_fortune)
                ExpandedMenuModel.MenuItem.RULES -> openScreen(R.id.nav_terms)
                ExpandedMenuModel.MenuItem.NEWS -> openScreen(R.id.nav_news)
                ExpandedMenuModel.MenuItem.MESSAGES -> openScreen(R.id.nav_messages)
                ExpandedMenuModel.MenuItem.LOYALTY_PROGRAM -> openScreen(R.id.nav_loyalty_program)
                ExpandedMenuModel.MenuItem.HALL_OF_FAME -> openScreen(R.id.nav_hall_of_fame)
                ExpandedMenuModel.MenuItem.EXIT -> mainViewModel!!.logout()
                else -> {}
            }

            if (ExpandedMenuModel.MenuItem.CASH_BOX != selectedMenuItem) {
                drawer.closeDrawers()
            }
            false
        }

        binding!!.navigationMenu.setOnChildClickListener { expandableListView: ExpandableListView?, view: View?, groupPosition: Int, childPosition: Int, l: Long ->
            val selectedMenuItem = menuAdapter!!.getMenuItemByPosition(groupPosition)
            if (ExpandedMenuModel.MenuItem.CASH_BOX == selectedMenuItem) {
                val selectedChild = menuAdapter!!.getCashBoxMenuItemByPosition(childPosition)
                when (selectedChild) {
                    ExpandedMenuModel.MenuItem.CHARITY -> {
                        sendAnalytics(
                            this,
                            AnalyticsEvent.MENU_CASHIER,
                            null,
                            null
                        )
                        openPaymentsScreen(PaymentsFragment.Screen.DEPOSIT)
                    }

                    ExpandedMenuModel.MenuItem.EXTRACT -> {
                        sendAnalytics(
                            this,
                            AnalyticsEvent.MENU_CASHIER,
                            null,
                            null
                        )
                        openPaymentsScreen(PaymentsFragment.Screen.EXTRACT)
                    }

                    ExpandedMenuModel.MenuItem.HISTORY -> openPaymentsScreen(PaymentsFragment.Screen.HISTORY)
                    else -> {}
                }
                drawer.closeDrawers()
            }
            false
        }

        binding!!.navFooterView.btnSupportChat.setOnClickListener { v: View? ->
            openScreen(R.id.nav_support_chat)
            drawer.closeDrawers()
        }
    }

    private fun registerFacebookLoginListener() {
        LoginManager.getInstance()
            .registerCallback(callbackManager, object : FacebookCallback<LoginResult> {
                override fun onSuccess(loginResult: LoginResult) {
                    sendSocialTokenToL4P(loginResult.accessToken.token, SocialNetwork.FACEBOOK)
                }

                override fun onCancel() {
                    val accessToken = AccessToken.getCurrentAccessToken()
                    if (accessToken != null) {
                        sendSocialTokenToL4P(accessToken.token, SocialNetwork.FACEBOOK)
                    }
                }

                override fun onError(error: FacebookException) {
                    showMessage(error.message!!)
                }
            })
    }

    private fun registerRequestPermissionNotificationLauncher() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            requestPermissionNotificationLauncher = registerForActivityResult<String, Boolean>(
                ActivityResultContracts.RequestPermission()
            ) { _: Boolean? ->
                if (ContextCompat.checkSelfPermission(
                        this,
                        Manifest.permission.POST_NOTIFICATIONS
                    ) == PackageManager.PERMISSION_DENIED
                ) {
                    showNotificationsPermissionDeniedDialog(this@MainActivity)
                }
            }
        }
    }

    fun requestNotificationPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            createNotificationChannel(this@MainActivity)

            if (ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS) !=
                PackageManager.PERMISSION_GRANTED
            ) {
                requestPermissionNotificationLauncher!!.launch(Manifest.permission.POST_NOTIFICATIONS)
            }
        }
    }

    fun sendSocialTokenToL4P(accessToken: String, socialNetwork: SocialNetwork) {
        val currentFragment = currentFragment
        if (currentFragment is EnterFragment) {
            currentFragment.showSocialNetworkLoader(socialNetwork)
        }

        sendSocialTokenToL4P(
            this@MainActivity,
            socialNetwork.value, accessToken,
            object : L4PAuthListener {
                override fun onSuccess(L4PToken: String) {
                    socialLogin(L4PToken)
                }

                override fun onFailure(error: String) {
                    showMessage(error)
                    val currentFragment: Fragment? = <EMAIL>
                    if (currentFragment is EnterFragment) {
                        currentFragment.hideSocialNetworkLoader(socialNetwork)
                    }
                }
            })
    }

    @JvmOverloads
    fun openEnterScreen(isLogin: Boolean, deepLinkUrl: String? = null) {
        if (navController!!.currentDestination != null
            && navController!!.currentDestination!!.id != R.id.nav_enter
        ) {
            showEnterScreen(isLogin, deepLinkUrl)
        }
    }

    fun openRulesScreen() {
        if (navController!!.currentDestination != null
            && navController!!.currentDestination!!.id != R.id.nav_terms
        ) {
            navController!!.navigate(
                R.id.nav_terms, null,
                NavOptions.Builder().setPopUpTo(R.id.nav_home, false).build()
            )
        }
    }

    fun openForgotPassScreen() {
        if (navController!!.currentDestination != null
            && navController!!.currentDestination!!.id != R.id.nav_recovery
        ) {
            navController!!.navigate(R.id.nav_recovery, null)
        }
    }

    fun openProfileScreen() {
        openScreen(R.id.nav_profile)
    }

    private fun openScreen(navId: Int) {
        if (navController!!.currentDestination != null && navController!!.currentDestination!!
                .id != navId
        ) {
            navController!!.navigate(
                navId, null,
                NavOptions.Builder().setPopUpTo(R.id.nav_home, R.id.nav_home == navId).build()
            )
        }
    }

    private fun openScreenWithoutPopUpToHome(navId: Int) {
        if (navController!!.currentDestination != null && navController!!.currentDestination!!
                .id != navId
        ) {
            navController!!.navigate(navId)
        }
    }

    fun openSupportChat(gameUrl: String?, fullGameUrl: String?) {
        openScreen(R.id.nav_support_chat)
        val bundle = Bundle()
        bundle.putString(GAME_URL, gameUrl)
        bundle.putString(FULL_GAME_URL, fullGameUrl)
        navController!!.navigate(
            R.id.nav_support_chat, bundle,
            NavOptions.Builder().setPopUpTo(R.id.nav_home, false).build()
        )
    }

    fun openSupportChat(messageText: String?) {
        openScreen(R.id.nav_support_chat)
        val bundle = Bundle()
        bundle.putString(MESSAGE_TEXT, messageText)
        navController!!.navigate(
            R.id.nav_support_chat, bundle,
            NavOptions.Builder().setPopUpTo(R.id.nav_home, false).build()
        )
    }

    fun openBiometricInputPasswordScreen() {
        navController!!.navigate(R.id.nav_biometric_input_password)
    }

    fun openHomeScreen() {
        openScreen(R.id.nav_home)
    }

    /**
     * @param isLock will lock clicks on Toolbar buttons if == true
     */
    fun lockToolbarBtnsClick(isLock: Boolean) {
        isToolbarBtnsClickable = !isLock
        if (isLock) {
            binding!!.drawerLayout.setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED)
            drawerToggle?.isDrawerIndicatorEnabled = false
        } else {
            binding!!.drawerLayout.setDrawerLockMode(DrawerLayout.LOCK_MODE_UNLOCKED)
            drawerToggle?.isDrawerIndicatorEnabled = true
        }
    }

    @JvmOverloads
    fun openPaymentsScreen(
        screen: PaymentsFragment.Screen,
        depositUrl: String? = null,
        fullGameUrl: String? = null
    ) {
        val bundle = Bundle()
        bundle.putString(PaymentsFragment.KEY_SCREEN_NAME, screen.name)
        bundle.putString(PaymentsFragment.KEY_DEPOSIT_URL, depositUrl)
        bundle.putString(PaymentsFragment.KEY_FULL_GAME_URL, fullGameUrl)
        navController!!.navigate(
            R.id.nav_payments, bundle,
            NavOptions.Builder().setPopUpTo(R.id.nav_home, false).build()
        )
    }

    fun openPaymentsScreenWithoutClearStack(screen: PaymentsFragment.Screen) {
        val bundle = Bundle()
        bundle.putString(PaymentsFragment.KEY_SCREEN_NAME, screen.name)
        navController!!.navigate(R.id.nav_payments, bundle)
    }

    fun openNewsInternalScreen(newsItem: NewsItem?) {
        val bundle = Bundle()
        bundle.putSerializable(NewsInternalFragment.NEWS_ITEM_KEY, newsItem)
        if (navController != null) {
            navController!!.navigate(R.id.nav_news_internal, bundle)
        }
    }

    fun openNewsInternalScreen(newsUrl: String) {
        getNewsItemByUrl(newsUrl, object : GenericTarget<LocalNews> {
            override fun onSuccess(localNews: LocalNews?) {
                if (localNews == null) {
                    return
                }
                val newsItem = NewsItem(NewsItem.ItemType.CARD_NEWS, localNews)
                runOnUiThread {
                    val bundle = Bundle()
                    bundle.putSerializable(NewsInternalFragment.NEWS_ITEM_KEY, newsItem)
                    navController!!.navigate(R.id.nav_news, bundle)
                }
            }

            override fun onError(
                errorMessage: String,
                errorCode: String,
                fieldsErrors: FieldsErrorsHolder
            ) {
                runOnUiThread {
                    if (lifecycle.currentState.isAtLeast(Lifecycle.State.RESUMED)) {
                        Toast.makeText(
                            this@MainActivity,
                            errorMessage,
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }
            }

            override fun onFailure(e: ApolloException) {
                Log.e(TAG, "Failed to load newsItemByUrl: " + e.localizedMessage)
                runOnUiThread {
                    if (lifecycle.currentState.isAtLeast(Lifecycle.State.RESUMED)) {
                        Toast.makeText(
                            this@MainActivity,
                            e.localizedMessage,
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }
            }
        })
    }

    fun openPromotionsScreen(returnHome: Boolean) {
        if (returnHome) {
            openScreen(R.id.nav_promotions)
        } else {
            navController!!.navigate(R.id.nav_promotions_w_back_icon, null)
        }
    }

    fun openExchangePointsScreen() {
        openScreen(R.id.nav_exchange_points)
    }

    fun openNewPasswordScreen(resetPasswordToken: String?) {
        val bundle = Bundle()
        bundle.putString(NewPasswordFragment.KEY_RESET_PASSWORD_TOKEN, resetPasswordToken)
        navController!!.navigate(
            R.id.nav_new_password, bundle,
            NavOptions.Builder().setPopUpTo(R.id.nav_home, false).build()
        )
    }

    private fun showLoginScreen(deepLinkUrl: String?) {
        val bundle = Bundle()
        bundle.putBoolean(EnterFragment.IS_LOGIN_KEY, false)
        bundle.putString(EnterFragment.DEEP_LINK_URL, deepLinkUrl)
        navController!!.navigate(
            R.id.nav_enter, bundle,
            NavOptions.Builder().setPopUpTo(R.id.nav_home, false).build()
        )
        binding!!.appBar.bToolbarButton.setText(R.string._login)
    }

    @JvmOverloads
    fun showEnterScreen(isLogin: Boolean, deepLinkUrl: String? = null) {
        val bundle = Bundle()

        // TODO: 2019-12-19 add transition animations
        bundle.putBoolean(EnterFragment.IS_LOGIN_KEY, isLogin)
        bundle.putString(EnterFragment.DEEP_LINK_URL, deepLinkUrl)
        navController!!.navigate(
            R.id.nav_enter, bundle,
            NavOptions.Builder().setPopUpTo(R.id.nav_home, false).build()
        )
        binding!!.appBar.bToolbarButton.setText(if (isLogin) R.string._registration else R.string._login)
    }

    fun showEnterScreenFromTournaments(isLogin: Boolean, tournamentItem: TournamentsItem?) {
        val bundle = Bundle()
        bundle.putBoolean(EnterFragment.IS_LOGIN_KEY, isLogin)
        bundle.putSerializable(TOURNAMENT_ITEM, tournamentItem)
        bundle.putSerializable(KEY_SCREEN, Screen.TOURNAMENT)
        navController!!.navigate(
            R.id.nav_enter, bundle,
            NavOptions.Builder().setPopUpTo(R.id.nav_home, false).build()
        )
        binding!!.appBar.bToolbarButton.setText(if (isLogin) R.string._registration else R.string._login)
    }

    fun showEnterScreenFromPregame(isLogin: Boolean, tournamentItem: TournamentsItem?) {
        val bundle = Bundle()
        bundle.putBoolean(EnterFragment.IS_LOGIN_KEY, isLogin)
        bundle.putSerializable(TOURNAMENT_ITEM, tournamentItem)
        bundle.putSerializable(KEY_SCREEN, Screen.PREGAME)
        navController!!.navigate(
            R.id.nav_enter, bundle,
            NavOptions.Builder().setPopUpTo(R.id.nav_home, false).build()
        )
        binding!!.appBar.bToolbarButton.setText(if (isLogin) R.string._registration else R.string._login)
    }

    fun showEnterScreenFromLottery(isLogin: Boolean, lotteryItem: LocalLottery?) {
        val bundle = Bundle()
        bundle.putBoolean(EnterFragment.IS_LOGIN_KEY, isLogin)
        bundle.putSerializable(LOTTERY_ITEM, lotteryItem)
        navController!!.navigate(
            R.id.nav_enter, bundle,
            NavOptions.Builder().setPopUpTo(R.id.nav_home, false).build()
        )
        binding!!.appBar.bToolbarButton.setText(if (isLogin) R.string._registration else R.string._login)
    }

    private fun processLoyaltyStatus(loyaltyProgress: GetInitialViewerDataQuery.LoyaltyProgress) {
        val loyaltyStatus = loyaltyProgress.status
        if (loyaltyStatus != null) {
            val prizes = loyaltyStatus.prizes
            var prizePoints = ""
            var prizeMoney = 0.0
            var prizeCurrencySymbol = ""
            if (!prizes.isNullOrEmpty()) {
                for (prize in prizes) {
                    if (prize?.asLoyaltyPointsPrizeType != null) {
                        prizePoints = prize.asLoyaltyPointsPrizeType.count.toString()
                    } else if (prize?.asLoyaltyMoneyPrizeType != null) {
                        prizeMoney = prize.asLoyaltyMoneyPrizeType.amount.toString().toDouble()
                        prizeCurrencySymbol = prize.asLoyaltyMoneyPrizeType.currency.symbol
                    }
                }
            }

            val localLoyaltyStatus = LoyaltyStatus(
                loyaltyStatus.id,
                loyaltyStatus.title, prizePoints, prizeMoney, prizeCurrencySymbol
            )
            processLoyaltyStatus(localLoyaltyStatus)
        }
    }

    private fun processLoyaltyStatus(loyaltyStatus: SubscribeLoyaltyStatusSubscription.ViewerLoyaltyStatus) {
        val prizes = loyaltyStatus.prizes
        var prizePoints = ""
        var prizeMoney = 0.0
        var prizeCurrencySymbol = ""
        if (!prizes.isNullOrEmpty()) {
            for (prize in prizes) {
                if (prize?.asLoyaltyPointsPrizeType != null) {
                    prizePoints = prize.asLoyaltyPointsPrizeType.count.toString()
                } else if (prize?.asLoyaltyMoneyPrizeType != null) {
                    prizeMoney = prize.asLoyaltyMoneyPrizeType.amount.toString().toDouble()
                    prizeCurrencySymbol = prize.asLoyaltyMoneyPrizeType.currency.symbol
                }
            }
        }

        val localLoyaltyStatus = LoyaltyStatus(
            loyaltyStatus.id, loyaltyStatus.title,
            prizePoints, prizeMoney, prizeCurrencySymbol
        )

        val currentFragment = currentFragment
        if (currentFragment is WheelOfFortuneFragment || currentFragment is GameFragment) {
            postponeLoyaltyStatus(localLoyaltyStatus)
        } else {
            processLoyaltyStatus(localLoyaltyStatus)
        }
    }

    private fun postponeLoyaltyStatus(localLoyaltyStatus: LoyaltyStatus) {
        Settings.get().postponedLoyaltyStatus = localLoyaltyStatus
    }

    private fun processLoyaltyStatus(localLoyaltyStatus: LoyaltyStatus) {
        val currentLoyaltyStatus = Settings.get().currentLoyaltyStatus
        if (currentLoyaltyStatus?.statusId != null && localLoyaltyStatus.statusId != null && localLoyaltyStatus.statusId > currentLoyaltyStatus.statusId) {
            showNewLoyaltyStatus(localLoyaltyStatus)
        }
        Settings.get().currentLoyaltyStatus = localLoyaltyStatus
    }

    private fun processPostponedLoyaltyStatus(destinationId: Int) {
        val postponedLoyaltyStatus = Settings.get().postponedLoyaltyStatus
        if (postponedLoyaltyStatus != null && R.id.nav_wheel_of_fortune != destinationId && R.id.nav_game_search != destinationId && R.id.nav_game != destinationId) {
            clearPostponedLoyaltyStatus()
            processLoyaltyStatus(postponedLoyaltyStatus)
        }
    }

    private fun clearPostponedLoyaltyStatus() {
        Settings.get().postponedLoyaltyStatus = null
    }

    private fun showNewLoyaltyStatus(loyaltyStatus: LoyaltyStatus) {
        val bundle = Bundle()
        bundle.putParcelable(NewLoyaltyStatusFragment.KEY_LOYALTY_STATUS, loyaltyStatus)
        navController!!.navigate(
            R.id.nav_new_loyalty_status, bundle,
            NavOptions.Builder().setPopUpTo(R.id.nav_home, false).build()
        )
    }

    fun hideToolbar() {
        binding!!.appBar.toolbar.visibility = View.GONE
    }

    fun showToolbar() {
        binding!!.appBar.toolbar.visibility = View.VISIBLE
    }

    fun showPregame(game: LocalGameItem?, view: View?) {
        val navDestination = navController!!.currentDestination
        if (navDestination != null) {
            if (navDestination.id == R.id.nav_home) {
                navController!!.navigate(R.id.action_nav_home_to_pregame)
            } else if (navDestination.id == R.id.nav_game_search) {
                navController!!.navigate(R.id.action_nav_game_search_to_pregame)
            } else if (navDestination.id == R.id.nav_game_room) {
                navController!!.navigate(R.id.action_nav_game_room_to_pregame)
            } else if (navDestination.id == R.id.nav_tournament_internal) {
                val bundle = Bundle()
                bundle.putBoolean(FROM_TOURNAMENT, true)
                navController!!.navigate(R.id.action_tournamentInternal_to_pregame, bundle)
            } else if (navDestination.id == R.id.nav_game) {
                navController!!.navigate(R.id.action_nav_game_to_nav_pregameFragment)
            }

            if (SessionDataHolder.getInstance().currentGameItem == null) {
                FirebaseCrashlytics.getInstance().log("showPregame(): gameThumb is null")
            }
        }
    }

    fun showPregame(autoJoinToTournament: Boolean) {
        val navDestination = navController!!.currentDestination
        if (navDestination != null) {
            if (navDestination.id == R.id.nav_home) {
                val bundle = Bundle()
                bundle.putSerializable(AUTO_JOIN, autoJoinToTournament)
                navController!!.navigate(R.id.action_nav_home_to_pregame, bundle)
            }
        }
    }

    fun showTournamentInternal(game: TournamentsItem?, autoJoin: Boolean) {
        //navController.navigate(R.id.action_nav_tournaments_to_tournamentInternalFragment);

        val bundle = Bundle()
        bundle.putSerializable(ITEM, game)
        bundle.putSerializable(AUTO_JOIN, autoJoin)
        navController!!.navigate(R.id.nav_tournament_internal, bundle)
    }

    fun showTournamentInternal(tournamentId: Int) {
        mainViewModelKt!!.getTournament(tournamentId)
    }

    fun showLotteryInternal(lottery: LocalLottery?) {
        val bundle = Bundle()
        bundle.putSerializable(ITEM, lottery)
        navController!!.navigate(R.id.nav_lottery_internal, bundle)
    }

    fun showLotteryInternal(lotteryId: Int, isNeedOpenResults: Boolean) {
        val bundle = Bundle()
        bundle.putInt(LOTTERY_ID, lotteryId)
        bundle.putBoolean(IS_NEED_OPEN_RESULTS, isNeedOpenResults)
        navController!!.navigate(R.id.nav_lottery_internal, bundle)
    }

    fun openGameWithPregame(gameUrl: String, gameType: String?) {
        SessionDataHolder.getInstance().currentGameImage = null
        loadGameInfo(gameUrl, object : GameInfoListener {
            override fun onGameInfoLoaded(gameThumb: GameThumb?) {
                if (gameThumb != null) {
                    SessionDataHolder.getInstance().currentGameItem = LocalGameItem(gameThumb)

                    val navDestination = navController!!.currentDestination
                    if (navDestination != null) {
                        val bundle = Bundle()
                        bundle.putString(PregameFragment.GAME_TYPE_KEY, gameType)
                        if (navDestination.id == R.id.nav_home) {
                            navController!!.navigate(R.id.action_nav_home_to_pregame, bundle)
                        } else {
                            navController!!.navigate(
                                R.id.nav_pregameFragment, bundle,
                                NavOptions.Builder().setPopUpTo(R.id.nav_home, false).build()
                            )
                        }
                    }
                } else {
                    showMessage(getString(R.string.something_goes_wrong))
                    FirebaseCrashlytics.getInstance()
                        .log("openGameWithPregame(): gameThumb is null. gameUrl: $gameUrl")
                }
            }
        })
    }

    fun openPregame(gameUrl: String) {
        SessionDataHolder.getInstance().currentGameImage = null
        loadGameInfo(gameUrl, object : GameInfoListener {
            override fun onGameInfoLoaded(gameThumb: GameThumb?) {
                if (gameThumb != null) {
                    SessionDataHolder.getInstance().currentGameItem = LocalGameItem(gameThumb)
                    navController!!.navigate(R.id.nav_pregameFragment)
                } else {
                    showMessage(getString(R.string.something_goes_wrong))
                    FirebaseCrashlytics.getInstance()
                        .log("openPregame(): gameThumb is null. gameUrl: $gameUrl")
                }
            }
        })
    }

    private fun loadGameInfo(gameUrl: String, gameInfoListener: GameInfoListener) {
        val shortGameUrl = gameUrl.substring(gameUrl.lastIndexOf("/") + 1)

        mainViewModelKt!!.getGameByUrl(shortGameUrl, object : GenericTarget<GameThumb?> {
            override fun onSuccess(gameThumb: GameThumb?) {
                runOnUiThread { gameInfoListener.onGameInfoLoaded(gameThumb) }
            }

            override fun onError(
                errorMessage: String,
                errorCode: String,
                fieldsErrors: FieldsErrorsHolder
            ) {
                runOnUiThread {
                    if (lifecycle.currentState.isAtLeast(Lifecycle.State.RESUMED)) {
                        Toast.makeText(
                            this@MainActivity,
                            errorMessage,
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }
            }

            override fun onFailure(e: ApolloException) {
                Log.e(TAG, "Failed to load gameInfo: " + e.localizedMessage)
                runOnUiThread {
                    if (lifecycle.currentState.isAtLeast(Lifecycle.State.RESUMED)) {
                        Toast.makeText(
                            this@MainActivity,
                            e.localizedMessage,
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }
            }
        })
    }

    fun openGameActivity(gameUrl: String?, fullUrl: String?, tournamentId: Int) {
        val navDestination = navController!!.currentDestination
        if (navDestination != null) {
            val bundle = Bundle()
            bundle.putString(GameFragment.URL_KEY, gameUrl)
            bundle.putString(GameFragment.FULL_URL_KEY, fullUrl)
            bundle.putInt(GameFragment.TOURNAMENT_ID, tournamentId)
            bundle.putParcelable(GameFragment.FAST_CLICK_PAYMENT_SYSTEM, fastClickPaymentSystem)

            val navOptions = NavOptions.Builder()
                .setEnterAnim(0)
                .setExitAnim(0)
                .setPopEnterAnim(0)
                .setPopExitAnim(0)
                .setPopUpTo(R.id.nav_home, false).build()
            navController!!.navigate(R.id.nav_game, bundle, navOptions)
        }
    }

    val initialViewerData: Unit
        get() {
            mainViewModel!!.getInitialViewerData()
        }

    val unvisitedCount: Unit
        get() {
            mainViewModelKt!!.getUnvisitedCount()
        }

    fun balanceSubscribe() {
        mainViewModelKt!!.balanceSubscribe()
    }

    fun loyaltyPointsSubscribe() {
        mainViewModelKt!!.loyaltyPointsSubscribe()
    }

    fun loyaltyProgressSubscribe() {
        mainViewModelKt!!.loyaltyProgressSubscribe()
    }

    fun loyaltyStatusSubscribe() {
        mainViewModelKt!!.loyaltyStatusSubscribe()
    }

    fun loyaltyXOnPointsSubscribe() {
        mainViewModelKt!!.loyaltyXOnPointsSubscribe()
    }

    fun realTimeMessagesSubscribe() {
        mainViewModelKt!!.realTimeMessagesSubscribe()
    }

    fun messagesSubscribe() {
        mainViewModelKt!!.messagesSubscribe()
    }

    fun onlineUsersSubscribe() {
        mainViewModelKt!!.onlineUsersSubscribe()
    }

    fun promotionsCountSubscribe() {
        mainViewModelKt!!.promotionsCountSubscribe()
    }

    fun tournamentsCountSubscribe() {
        mainViewModelKt!!.tournamentsCountSubscribe()
    }

    fun lotteriesCountSubscribe() {
        mainViewModelKt!!.lotteriesCountSubscribe()
    }

    val currentFragment: Fragment?
        get() {
            val navHostFragment =
                supportFragmentManager.findFragmentById(R.id.nav_host_fragment)
            var currentFragment: Fragment? = null
            if (navHostFragment != null) {
                currentFragment = navHostFragment.childFragmentManager.fragments[0]
            }
            return currentFragment
        }

    fun getLoyaltyPoints(): Int {
        return loyaltyPoints.toInt()
    }

    private fun showBonusWagering() {
        bonusWageringView!!.visibility = View.VISIBLE
        val tvBalanceDrawerLp = tvBalanceDrawer!!.layoutParams
        if (tvBalanceDrawerLp is MarginLayoutParams) {
            val topMargin =
                resources.getDimensionPixelSize(R.dimen.tv_balance_drawer_with_bonus_top_margin)
            tvBalanceDrawerLp.topMargin = topMargin
        }
    }

    private fun hideBonusWagering() {
        bonusWageringView!!.visibility = View.GONE
        val tvBalanceDrawerLp = tvBalanceDrawer!!.layoutParams
        if (tvBalanceDrawerLp is MarginLayoutParams) {
            val topMargin =
                resources.getDimensionPixelSize(R.dimen.tv_balance_drawer_without_bonus_top_margin)
            tvBalanceDrawerLp.topMargin = topMargin
        }
    }

    private interface GameInfoListener {
        fun onGameInfoLoaded(gameThumb: GameThumb?)
    }

    @Synchronized
    fun resolveCloudflare(isCaptchaMode: Boolean) {
        val SHOW_RELOAD_CAPTCHA_TIME = 15000
        val SHOW_RELOAD_JS_CHALLENGE_TIME = 7000
        val showReloadTime =
            if (isCaptchaMode) SHOW_RELOAD_CAPTCHA_TIME else SHOW_RELOAD_JS_CHALLENGE_TIME
        if (cloudflareIsResolving) {
            return
        }
        cloudflareIsResolving = true

        runOnUiThread {
            dialogCloudflare = Dialog(
                this@MainActivity,
                android.R.style.Theme_Black_NoTitleBar_Fullscreen
            )
            dialogCloudflare!!.setContentView(R.layout.dialog_cloudflare)

            val cookieManager = CookieManager.getInstance()

            val handler = Handler(Looper.getMainLooper())
            handler.postDelayed({
                dialogCloudflare!!.findViewById<View>(R.id.container_reload).visibility =
                    View.VISIBLE
            }, showReloadTime.toLong())

            val webView = dialogCloudflare!!.findViewById<WebView>(R.id.wv)
            webView.settings.javaScriptEnabled = true
            webView.settings.userAgentString = userAgent
            webView.setBackgroundColor(Color.TRANSPARENT)
            webView.settings.domStorageEnabled = true
            webView.webChromeClient = WebChromeClient()
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                cookieManager.setAcceptThirdPartyCookies(webView, true)
                cookieManager.setAcceptCookie(true)
            }
            val btnReload = dialogCloudflare!!.findViewById<Button>(R.id.btn_reload)
            btnReload.setOnClickListener { v: View? -> webView.reload() }

            webView.webViewClient = object : WebViewClient() {
                override fun onPageStarted(
                    view: WebView,
                    url: String,
                    favicon: Bitmap?
                ) {
                    super.onPageStarted(view, url, favicon)
                    mainViewModelKt!!.checkApiAccess()
                }
            }

            webView.loadUrl(ApoloConfig.BASE_URL)
            dialogCloudflare!!.show()
        }
    }

    private fun noConnectionUpdate() {
        val currentFragment = currentFragment
        if (currentFragment is NoConnectionFragment) {
            currentFragment.update()
        }
    }

    fun resetMessagesCounter() {
        headerMessagesCounter!!.visibility = View.INVISIBLE
        menuAdapter!!.resetMessagesCounter()
    }

    fun setMessagesCounter(count: Int) {
        if (count > 0) {
            headerMessagesCounter!!.visibility = View.VISIBLE
            tvHeaderMessagesCounter!!.text = count.toString()
        } else {
            headerMessagesCounter!!.visibility = View.INVISIBLE
        }
        menuAdapter!!.setMessagesCounter(count)
    }

    private fun resetDrawerCounters() {
        menuAdapter!!.setPromotionsCounter(0)
        menuAdapter!!.setTournamentsCounter(0)
        menuAdapter!!.setLotteriesCounter(0)
    }

    fun socialLogin(accessToken: String) {
        mainViewModelKt!!.socialLogin(
            accessToken,
            GeneralTools.getLocale(this@MainActivity).language
        )
    }

    companion object {
        private val TAG: String = MainActivity::class.java.simpleName
        const val KEY_SCREEN: String = "keyScreen"
        const val TOURNAMENT_ID: String = "tournamentId"
        const val GAME_URL: String = "gameUrl"
        const val MESSAGE_TEXT: String = "messageText"
        const val FULL_GAME_URL: String = "fullGameUrl"
        const val DEPOSIT_URL: String = "depositUrl"
        const val TOURNAMENT_ITEM: String = "tournamentItem"
        const val LOTTERY_ITEM: String = "lotteryItem"
        const val LOTTERY_ID: String = "lotteryId"
        const val IS_NEED_OPEN_RESULTS: String = "isNeedOpenResults"
        const val FROM_TOURNAMENT: String = "fromTournament"
        const val ITEM: String = "item"
        const val AUTO_JOIN: String = "autoJoin"
    }
}
