package com.abrand.custom.domain

import android.util.Log
import com.abrand.custom.data.entity.AllDomainsBannedException
import com.abrand.custom.data.entity.LocalConfig
import com.abrand.custom.data.repositories.SettingsRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.single

class GeneratorCommunicationUseCase(
    private val settingsRepository: SettingsRepository = SettingsRepository,
    private val makeInitCustomerRequestUseCase: MakeInitCustomerRequestUseCase = MakeInitCustomerRequestUseCase(),
    private val makeCheckRequestUseCase: MakeCheckRequestUseCase = MakeCheckRequestUseCase(),
    private val makeGetInstalledRequestUseCase: MakeGetInstalledRequestUseCase = MakeGetInstalledRequestUseCase(),
    private val sendBannedDomainsUseCase: SendBannedDomainsUseCase = SendBannedDomainsUseCase(),
) {
    private var isFirstSetup: Boolean = false

    suspend operator fun invoke(appConfig: LocalConfig? = null): Flow<LocalConfig> {
        val initialConfig = appConfig ?: settingsRepository.getLocalConfig()

        return flow { emit(initialConfig) }
            .map { config -> makeInitCustomerRequest(config) }
            .map { config -> makeCheckRequestUseCase(config) }
            .map { config -> makeGetInstalledRequest(isFirstSetup, config) }
            .map { config -> sendBannedDomainsUseCase(config) }
            .map { config -> updateLocalConfig(config) }
            // TODO: process config data after communication
            .catch { error ->
                emit(buildAppConfigForErrorCases(error, initialConfig))
            }
    }

    private suspend fun makeInitCustomerRequest(
        config: LocalConfig
    ): LocalConfig {
        return when {
            config.customerToken.isNullOrBlank() -> {
                isFirstSetup = true
                makeInitCustomerRequestUseCase(config)
            }

            else -> config
        }
    }

    private suspend fun makeGetInstalledRequest(
        isFirstSetup: Boolean,
        config: LocalConfig
    ): LocalConfig {
        return when {
            isFirstSetup -> {
                makeGetInstalledRequestUseCase(config)
            }

            else -> config
        }
    }

    private suspend fun buildAppConfigForErrorCases(
        error: Throwable,
        config: LocalConfig
    ): LocalConfig {
        return when (error) {
            is AllDomainsBannedException -> {
//                val reservedDomainList = getReservedDomainListUseCase(config) // TODO implement later at STPMOB-9040
                val reservedDomainList = emptyList<String>()

                @Suppress("KotlinConstantConditions") // TODO remove after implementing getReservedDomainListUseCase
                when {
                    reservedDomainList.isNotEmpty() -> {
                        val configWithReservedDomain = config.copy(domains = reservedDomainList)

                        <EMAIL>(configWithReservedDomain).single()
                    }

                    else -> {
                        config
                    }
                }
            }

            else -> {
                config
            }
        }
    }

    private fun updateLocalConfig(config: LocalConfig): LocalConfig {
        settingsRepository.saveLocalConfig(config)
        return config
    }
}
