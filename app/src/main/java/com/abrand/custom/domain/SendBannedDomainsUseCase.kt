package com.abrand.custom.domain

import com.abrand.custom.data.entity.LocalConfig
import com.abrand.custom.data.repositories.BannedDomainsRepositoryImpl

class SendBannedDomainsUseCase(
    private val bannedDomainsRepository: BannedDomainsRepository = BannedDomainsRepositoryImpl,
) {
    suspend operator fun invoke(appConfig: LocalConfig): LocalConfig {
        val bannedDomains = bannedDomainsRepository.getBannedDomainsList().ifEmpty {
            return appConfig
        }
        val currentTime = System.currentTimeMillis().apply {
            if (isSentFailedRecently(this)) return appConfig
        }

        val result = bannedDomainsRepository.sendBannedDomains(bannedDomains, appConfig)

        return when (result.status) {
            true -> {
                bannedDomainsRepository.deleteAllBannedDomains()
                appConfig.copy(
                    domains = result.domains,
                    updaterDomains = result.updaterDomains
                )
            }

            else -> {
                bannedDomainsRepository.saveLastFailedBannedDomainsRequestTimestamp(currentTime)
                appConfig
            }
        }
    }

    private suspend fun isSentFailedRecently(currentTime: Long): Boolean {
        val lastFailedAttemptTimestamp =
            bannedDomainsRepository.getLastFailedBannedDomainsRequestTimestamp()

        return currentTime - lastFailedAttemptTimestamp < FAILED_ATTEMPT_MIN_PERIOD_IN_MILLISECONDS
    }

    companion object {
        private const val FAILED_ATTEMPT_MIN_PERIOD_IN_MILLISECONDS = 24 * 60 * 60 * 1000 // 1 day
    }
}
