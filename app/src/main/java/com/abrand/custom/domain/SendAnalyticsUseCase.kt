package com.abrand.custom.domain

import com.abrand.custom.data.entity.LocalConfig
import com.abrand.custom.data.repositories.GeneratorRepositoryImpl
import java.net.URI
import java.net.URLEncoder
import java.nio.charset.StandardCharsets

class SendAnalyticsUseCase(
    private val generatorRepository: GeneratorRepository = GeneratorRepositoryImpl()
) {
    suspend operator fun invoke(url: String, appConfig: LocalConfig): LocalConfig {
        val updatedUrl = checkAndAddRefToLink(url, appConfig.ref.orEmpty())

        generatorRepository.sendAnalytics(url = updatedUrl.toString())
        return appConfig
    }

    private fun checkAndAddRefToLink(url: String, ref: String): URI {
        val uri = URI(url)
        val queryParameters = uri.query?.split("&")?.associate {
            val split = it.split("=")
            split[0] to (split.getOrNull(1) ?: "")
        }?.toMutableMap() ?: mutableMapOf()

        val newQueryParameters = if (queryParameters.containsKey("ref")) {
            queryParameters.mapValues { (key, value) ->
                if (key == "ref") ref else value
            }
        } else {
            queryParameters + ("ref" to ref)
        }

        val newQuery = newQueryParameters.entries.joinToString("&") { (key, value) ->
            "${URLEncoder.encode(key, StandardCharsets.UTF_8.toString())}=${
                URLEncoder.encode(
                    value,
                    StandardCharsets.UTF_8.toString()
                )
            }"
        }

        return URI(
            uri.scheme,
            uri.authority,
            uri.path,
            newQuery,
            uri.fragment
        )
    }
}
