package com.abrand.custom.domain

import com.abrand.custom.data.entity.LocalConfig
import com.abrand.custom.data.repositories.GeneratorRepositoryImpl
import com.abrand.custom.tools.UrlEncoder

class MakeInitCustomerRequestUseCase(
    private val generatorRepository: GeneratorRepository = GeneratorRepositoryImpl(),
    private val encoder: UrlEncoder = UrlEncoder(),
) {
    suspend operator fun invoke(appConfig: LocalConfig): LocalConfig {
        val result = generatorRepository.initCustomer(
            urlParams = constructUrlParams(appConfig.uuid.orEmpty(), appConfig.token),
            uuid = appConfig.uuid.orEmpty(),
            refCode = appConfig.ref.orEmpty(),
            downloadCode = appConfig.downloadCode,
            domains = appConfig.domains,
        )
        return appConfig.copy(customerToken = result)
    }

    private fun constructUrlParams(uuid: String, appToken: String): String {
        return encoder.encode(
            encoder.encodeParam("d"),
            encoder.encodeParam("f", appToken),
            encoder.encodeParam("i", uuid)
        )
    }
}
