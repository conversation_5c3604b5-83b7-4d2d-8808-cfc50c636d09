package com.abrand.custom.network

import android.content.Context
import android.text.TextUtils
import android.util.Log
import com.abrand.custom.BuildConfig
import com.abrand.custom.data.Settings
import com.abrand.custom.data.entity.AnalyticsEvent
import com.abrand.custom.interfaces.UpdateImportanceTarget
import com.abrand.custom.network.UpdateApp.CheckUpdateResponse
import com.abrand.custom.tools.Crypt
import com.abrand.custom.tools.DateTools
import com.abrand.custom.tools.GeneralTools
import com.adjust.sdk.Adjust
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import kotlinx.coroutines.suspendCancellableCoroutine
import okhttp3.*
import okhttp3.HttpUrl.Companion.toHttpUrlOrNull
import okhttp3.logging.HttpLoggingInterceptor
import org.json.JSONException
import org.json.JSONObject
import java.io.IOException
import java.util.*
import java.util.concurrent.TimeUnit
import kotlin.coroutines.resume

object OkHttpProcessor {
    private const val TAG: String = "OkHttpProcessor"
    private val okHttpClient: OkHttpClient
    private val ANALYTICS_URL = Crypt.decrypt(BuildConfig.ANALYTICS_URL)
    private val checkApplicationVersionUrl = Crypt.decrypt(BuildConfig.UPDATER_URL)
    private val L4P_AUTH_URL = Crypt.decrypt(BuildConfig.L4P_AUTH_URL)
    private val L4P_API_KEY = Crypt.decrypt(BuildConfig.L4P_API_KEY)
    private val L4P_API_SECRET = Crypt.decrypt(BuildConfig.L4P_API_SECRET)

    init {
        val logging = HttpLoggingInterceptor()
        logging.level = (HttpLoggingInterceptor.Level.BODY)
        val clientBuilder = OkHttpClient.Builder()
            .addInterceptor(logging)
            .readTimeout(30, TimeUnit.SECONDS)
        okHttpClient = clientBuilder.build()
    }

    fun sendAnalytics(context: Context, event: AnalyticsEvent, jsonData: String?, analyticsListener: AnalyticsListener?) {
        Adjust.getGoogleAdId(context) { googleAdId: String? ->
            val urlBuilder = ANALYTICS_URL.toHttpUrlOrNull()!!.newBuilder()
            urlBuilder.addQueryParameter("adid", Adjust.getAdid())
            urlBuilder.addQueryParameter("gps_adid", googleAdId)
            urlBuilder.addQueryParameter("created_at", System.currentTimeMillis().toString())
            urlBuilder.addQueryParameter("event", event.eventToken)
            urlBuilder.addQueryParameter("event_name", event.eventName)
            if (!TextUtils.isEmpty(jsonData)) {
                urlBuilder.addQueryParameter("data", jsonData)
            }
            urlBuilder.addQueryParameter("push_token", Settings.get().pushToken)
            urlBuilder.addQueryParameter("random_user_id", GeneralTools.getAndroidId(context) + "_" + Settings.get().userId)
            val url = urlBuilder.build().toString()
            val request = Request.Builder()
                    .url(url)
                    .build()

            okHttpClient.newCall(request).enqueue(object : Callback {
                override fun onFailure(call: Call, e: IOException) {
                    Log.e(TAG, e.toString())
                    analyticsListener?.onFailure()
                }

                @Throws(IOException::class)
                override fun onResponse(call: Call, response: Response) {
                    if (response.isSuccessful) {
                        analyticsListener?.onSuccess()
                    } else {
                        Log.e(TAG, "Unexpected code $response")
                        analyticsListener?.onFailure()
                    }
                }
            })
        }
    }

    suspend fun getUpdateImportance(
        appVersion: String,
        url: String?,
        appToken: String
    ): CheckUpdateResponse = suspendCancellableCoroutine { cont ->
        getUpdateImportance(appVersion, url, appToken, object : UpdateImportanceTarget {
            override fun onSuccess(updateImportanceCode: Int, appUrl: String) {
                cont.resume(CheckUpdateResponse.Success(updateImportanceCode, appUrl))
            }

            override fun onError(error: String) {
                cont.resume(CheckUpdateResponse.ApplicationError(error))
            }
        })
    }

    private fun getUpdateImportance(appVersion: String, url: String?, appToken: String, target: UpdateImportanceTarget) {
        val updateUrl = url ?: checkApplicationVersionUrl
        val request = Request.Builder()
                .url(updateUrl)
                .addHeader("Content-Version", appVersion)
                .addHeader("x-application-token", appToken)
                .build()

        okHttpClient.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                // Transport layer fail
                Log.e(TAG, e.toString())
                target.onError(e.toString())
            }

            override fun onResponse(call: Call, response: Response) {
                if (response.isSuccessful) {
                    val jsonData: String = response.body?.string() ?: ""
                    if (!TextUtils.isEmpty(jsonData)) {
                        try {
                            val jsonObject = JSONObject(jsonData)
                            val updateLevel = jsonObject.getInt("update_level")
                            val url = jsonObject.getString("url")
                            target.onSuccess(updateLevel, url)
                        } catch ( e: JSONException ) {
                            Firebase.crashlytics.recordException(e)
                            target.onError("Can't parse JSON response $jsonData")
                        }
                    } else {
                        target.onError("Can't parse JSON response")
                    }
                } else {
                    val jsonData: String = response.body?.string() ?: ""
                    Log.e(TAG, "Unexpected code $response. jsonData: $jsonData")
                    target.onError("Unexpected code $response")
                }
            }
        })
    }

    fun doRequest(link: String) {
        val request = Request.Builder()
                .url(link)
                .build()

        okHttpClient.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                Log.e(TAG, e.toString())
            }

            override fun onResponse(call: Call, response: Response) {
                if (!response.isSuccessful) {
                    Log.e(TAG, "Unexpected code ${response.code}")
                }
            }
        })
    }

    fun sendSocialTokenToL4P(context: Context, socialNetwork: String, token: String, listener: L4PAuthListener) {
        val currentDate =
            DateTools.getFormattedDate(Date(), DateTools.getServerShortDateFormat(context))
        val code = "$L4P_API_KEY-$L4P_API_SECRET-$currentDate"
        val sig = GeneralTools.getBase64(GeneralTools.getMD5(code))

        val formBody: RequestBody = FormBody.Builder()
            .add("sig", sig)
            .add("client", socialNetwork)
            .add("token", token)
            .build()

        val request: Request = Request.Builder()
            .url(L4P_AUTH_URL)
            .post(formBody)
            .build()

        okHttpClient.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                Log.e(TAG, e.toString())
                listener.onFailure(e.toString())
            }

            override fun onResponse(call: Call, response: Response) {
                if (!response.isSuccessful) {
                    Log.e(TAG, "L4P. Unexpected code ${response.code}")
                    listener.onFailure("Unexpected code: ${response.code}")
                } else {
                    response.body?.let {
                        val json = JSONObject(it.string())
                        val token: String = json.get("token").toString()
                        listener.onSuccess(token)
                    }
                }
            }
        })
    }

    interface AnalyticsListener {
        fun onSuccess()
        fun onFailure()
    }

    interface L4PAuthListener {
        fun onSuccess(L4PToken: String)
        fun onFailure(error: String)
    }
}
