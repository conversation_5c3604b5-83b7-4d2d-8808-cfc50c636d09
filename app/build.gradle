plugins {
    id("com.apollographql.apollo3").version("3.0.0")
    id("org.jetbrains.kotlin.plugin.compose") version "2.0.20"
    id 'com.google.devtools.ksp' version '2.1.21-2.0.1'
}
apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-parcelize'
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'

//**************** Config *********************
apply from: "../common.gradle"
applyCommonXDependencies()
applyAdjustDependencies()

repositories {
    mavenCentral()
}

ext {
    APPLICATION_NAME        = "Armada"
    APPLICATION_ID          = "com.adm777.app"
    RELEASE_HOST            = "casinoarmada.com"
    RELEASE_RECAPTCHA_HOST  = "com.abrand.custom" //TODO Change to real?
    RELEASE_BASE_URL        = "https://casinoarmada.com/"
    RELEASE_ENDPOINT        = "https://casinoarmada.com/api-gateway/graphql"
    RELEASE_WWS_ENDPOINT    = "wss://casinoarmada.com/api-gateway/websocket"
    RELEASE_CAPTCHA_PREF    = "https://casinoarmada.com/captcha/image/"
    RELEASE_CAPTCHA_KEY     = "6LeW4GIrAAAAAKQwRNNA3I7fhysit6aCzWjbXj_y"
    RELEASE_ANALYTICS_URL   = "https://kauktripe.net/notify"
    ADJUST_ID               = "dmxr4bhse1og"
    UPDATE_URL              = "http://merypou5.com/check-application-version"
    UPDATE_TOKEN            = "1ee9ffb6-3f2e-64ca-b6a3-4f222b7a4bf4"
    RELEASE_L4P_AUTH_URL    = "https://login4play.com/api/social/auth"
    RELEASE_L4P_API_KEY     = "8348957923894583452346561876"
    RELEASE_L4P_API_SECRET  = "LGNJVFIrgjdowpeori903gk9eu834"
    REDIRECTOR_RELEASE_HOST_1 = "ad1.admitrack.com"
    REDIRECTOR_RELEASE_HOST_2 = "ad2.admitrack.com"
    REDIRECTOR_RELEASE_HOST_3 = "ad3.admitrack.com"
    REDIRECTOR_RELEASE_HOST_4 = "ad4.admitrack.com"
    REDIRECTOR_RELEASE_HOST_5 = "ad5.admitrack.com"
}

apollo {
//    generateKotlinModels.set(true) // or false for Java models
    useVersion2Compat()
}

android {
    namespace 'com.abrand.custom'
    buildFeatures {
        viewBinding true
        compose true
    }

    testOptions {
        unitTests {
            includeAndroidResources = true
        }
    }

    applyConfig(/*enable proguard*/ true)

    String MARKET_DESTINATION = "market"
    String PRODUCT_DESTINATION = "product"
    defaultConfig {
        versionCode 125
        versionName "1.9.0.dev-rc1.9.0"

        multiDexEnabled true

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
//        testInstrumentationRunner "android.test.InstrumentationTestRunner" //necessary for some tests

        buildConfigField "String", "MARKET_DESTINATION", "\"$MARKET_DESTINATION\""
        buildConfigField "String", "PRODUCT_DESTINATION", "\"$PRODUCT_DESTINATION\""
        buildConfigField "String", "ADJUST_ID", "\"$ADJUST_ID\""
        buildConfigField "String", "CAPTCHA_KEY", "\"$RELEASE_CAPTCHA_KEY\""
    }

    //====================Flavors==============================
    flavorDimensions "destination", "config"
    productFlavors {

        market {
            dimension "destination"
            addBuildConfigField(market, "DESTINATION", MARKET_DESTINATION, true)
        }

        product {
            dimension "destination"
            addBuildConfigField(product, "DESTINATION", PRODUCT_DESTINATION, true)
        }

        apkTesting {
            dimension "config"
            applicationId testAppId.trim()
            resValue "string", "app_name", "dev.${APPLICATION_NAME}"
            signingConfig signingConfigs.apkTesting
            manifestPlaceholders = [hostName: "$debugHost", redirectorHost1: "$debugRedirectorHost1",
                                    redirectorHost2: "$debugRedirectorHost2", redirectorHost3: "$debugRedirectorHost3",
                                    redirectorHost4: "$debugRedirectorHost4", redirectorHost5: "$debugRedirectorHost5",
                                    YANDEX_CLIENT_ID: "231c1a0caaf2433cac92e41204462bff"]

            addBuildConfigField(apkTesting, "HOST", debugHost.trim(), true)
            addBuildConfigField(apkTesting, "BASE_URL", debugBaseUrl.trim(), true)
            addBuildConfigField(apkTesting, "ENDPOINT", debugEndpoint.trim(), true)
            addBuildConfigField(apkTesting, "WWS_ENDPOINT", debugEndpoint.trim(), true)
            addBuildConfigField(apkTesting, "CAPTCHA_PREF", debugCaptchaPref.trim(), true)
            addBuildConfigField(apkTesting, "RECAPTCHA_HOST", debugReCaptchaHost.trim(), true)
            addBuildConfigField(apkTesting, "ANALYTICS_URL", debugAnalyticsUrl.trim(), true)
            addBuildConfigField(apkTesting, "UPDATER_URL", debug_update_url.trim(), true)
            addBuildConfigField(apkTesting, "UPDATER_TOKEN", debug_update_token.trim(), true)
            addBuildConfigField(apkTesting, "L4P_AUTH_URL", debugL4PAuthUrl.trim(), true)
            addBuildConfigField(apkTesting, "L4P_API_KEY", debugL4PApiKey.trim(), true)
            addBuildConfigField(apkTesting, "L4P_API_SECRET", debugL4PApiSecret.trim(), true)
        }

        productionTesting {
            dimension "config"
            applicationId testAppId.trim()
            resValue "string", "app_name", "prod_test.${APPLICATION_NAME}"
            signingConfig signingConfigs.apkTesting
            manifestPlaceholders = [hostName: "$RELEASE_HOST", redirectorHost1: "$REDIRECTOR_RELEASE_HOST_1",
                                    redirectorHost2: "$REDIRECTOR_RELEASE_HOST_2", redirectorHost3: "$REDIRECTOR_RELEASE_HOST_3",
                                    redirectorHost4: "$REDIRECTOR_RELEASE_HOST_4", redirectorHost5: "$REDIRECTOR_RELEASE_HOST_5",
                                    YANDEX_CLIENT_ID: "8ab2409c112a45269ae0a56dcd6b4a41"]

            addBuildConfigField(productionTesting, "HOST", RELEASE_HOST.trim(), true)
            addBuildConfigField(productionTesting, "BASE_URL", RELEASE_BASE_URL.trim(), true)
            addBuildConfigField(productionTesting, "ENDPOINT", RELEASE_ENDPOINT.trim(), true)
            addBuildConfigField(productionTesting, "WWS_ENDPOINT", RELEASE_WWS_ENDPOINT.trim(), true)
            addBuildConfigField(productionTesting, "CAPTCHA_PREF", RELEASE_CAPTCHA_PREF.trim(), true)
            addBuildConfigField(productionTesting, "RECAPTCHA_HOST", RELEASE_RECAPTCHA_HOST.trim(), true)
            addBuildConfigField(productionTesting, "ANALYTICS_URL", RELEASE_ANALYTICS_URL.trim(), true)
            addBuildConfigField(productionTesting, "UPDATER_URL", UPDATE_URL.trim(), true)
            addBuildConfigField(productionTesting, "UPDATER_TOKEN", UPDATE_TOKEN.trim(), true)
            addBuildConfigField(productionTesting, "L4P_AUTH_URL", RELEASE_L4P_AUTH_URL.trim(), true)
            addBuildConfigField(productionTesting, "L4P_API_KEY", RELEASE_L4P_API_KEY.trim(), true)
            addBuildConfigField(productionTesting, "L4P_API_SECRET", RELEASE_L4P_API_SECRET.trim(), true)
        }

        finalVersion {
            dimension "config"
            applicationId APPLICATION_ID
            resValue "string", "app_name", "${APPLICATION_NAME}"
            signingConfig signingConfigs.finalVersion
            manifestPlaceholders = [hostName: "$RELEASE_HOST", redirectorHost1: "$REDIRECTOR_RELEASE_HOST_1",
                                    redirectorHost2: "$REDIRECTOR_RELEASE_HOST_2", redirectorHost3: "$REDIRECTOR_RELEASE_HOST_3",
                                    redirectorHost4: "$REDIRECTOR_RELEASE_HOST_4", redirectorHost5: "$REDIRECTOR_RELEASE_HOST_5",
                                    YANDEX_CLIENT_ID: "8ab2409c112a45269ae0a56dcd6b4a41"]

            addBuildConfigField(finalVersion, "HOST", RELEASE_HOST.trim(), true)
            addBuildConfigField(finalVersion, "BASE_URL", RELEASE_BASE_URL.trim(), true)
            addBuildConfigField(finalVersion, "ENDPOINT", RELEASE_ENDPOINT.trim(), true)
            addBuildConfigField(finalVersion, "WWS_ENDPOINT", RELEASE_WWS_ENDPOINT.trim(), true)
            addBuildConfigField(finalVersion, "CAPTCHA_PREF", RELEASE_CAPTCHA_PREF.trim(), true)
            addBuildConfigField(finalVersion, "RECAPTCHA_HOST", RELEASE_RECAPTCHA_HOST.trim(), true)
            addBuildConfigField(finalVersion, "ANALYTICS_URL", RELEASE_ANALYTICS_URL.trim(), true)
            addBuildConfigField(finalVersion, "UPDATER_URL", UPDATE_URL.trim(), true)
            addBuildConfigField(finalVersion, "UPDATER_TOKEN", UPDATE_TOKEN.trim(), true)
            addBuildConfigField(finalVersion, "L4P_AUTH_URL", RELEASE_L4P_AUTH_URL.trim(), true)
            addBuildConfigField(finalVersion, "L4P_API_KEY", RELEASE_L4P_API_KEY.trim(), true)
            addBuildConfigField(finalVersion, "L4P_API_SECRET", RELEASE_L4P_API_SECRET.trim(), true)

            /* SAFETY */ applySafety(finalVersion)
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlinVersion"
    implementation 'androidx.navigation:navigation-fragment:2.3.3'
    implementation 'androidx.navigation:navigation-ui:2.3.3'
    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'androidx.viewpager2:viewpager2:1.0.0'
    implementation "com.google.android.material:material:1.2.1"
    implementation "androidx.core:core-ktx:1.13.1"
    implementation 'androidx.biometric:biometric:1.1.0'
    implementation 'androidx.fragment:fragment-ktx:1.3.6'
    implementation 'com.google.android.flexbox:flexbox:3.0.0'
    implementation "org.jetbrains.kotlinx:kotlinx-datetime:0.6.1"
    implementation 'androidx.compose.material3.adaptive:adaptive-android:1.0.0'

    def composeBom = platform('androidx.compose:compose-bom:2024.12.01')
    implementation composeBom
    implementation 'androidx.compose.material3:material3'
    implementation 'androidx.compose.ui:ui-tooling-preview'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7'
    debugImplementation 'androidx.compose.ui:ui-tooling'
    androidTestImplementation composeBom

    //tests
    testImplementation 'junit:junit:4.12'
    testImplementation 'org.robolectric:robolectric:4.3.1'
    testImplementation "org.mockito:mockito-core:3.3.3"
    androidTestImplementation 'androidx.fragment:fragment-testing:1.6.2'
    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'

    implementation "androidx.datastore:datastore-preferences:1.1.6"
    implementation "androidx.room:room-runtime:2.7.1"
    ksp 'androidx.room:room-compiler:2.7.1'
    implementation 'androidx.room:room-ktx:2.7.1'

    implementation 'com.apollographql.apollo3:apollo-runtime:3.0.0'
    implementation 'com.apollographql.apollo3:apollo-api:3.0.0'
//    implementation "com.apollographql.apollo3:apollo-android-support:3.0.0"
//    implementation "com.apollographql.apollo3:apollo-coroutines-support:2.5.4"
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.retrofit2:converter-scalars:2.9.0'
    implementation 'com.squareup.okhttp3:okhttp:4.9.1'
    implementation "com.squareup.okhttp3:logging-interceptor:4.9.1"

    // If not already on your classpath, you might need the jetbrains annotations
    compileOnly("org.jetbrains:annotations:13.0")
    testCompileOnly("org.jetbrains:annotations:13.0")
    implementation 'com.squareup.picasso:picasso:2.71828'
    implementation 'jp.wasabeef:blurry:4.0.0'
    implementation 'com.google.android.gms:play-services-safetynet:17.0.0' //TODO delete
    implementation "com.google.android.recaptcha:recaptcha:18.7.1"
    implementation "com.github.yandextaxitech:binaryprefs:1.0.1"
    implementation "androidx.security:security-crypto:1.0.0-beta01"
    implementation 'com.google.code.gson:gson:2.8.6'

    // Firebase
    implementation platform('com.google.firebase:firebase-bom:33.7.0')
    implementation 'com.google.firebase:firebase-messaging-ktx'
    implementation 'com.google.firebase:firebase-crashlytics-ktx'
    implementation 'com.google.firebase:firebase-analytics-ktx'

    // Social
    implementation 'com.vk:androidsdk:2.7.0'
    implementation 'com.yandex.android:authsdk:2.3.1'
    implementation 'com.facebook.android:facebook-login:8.1.0'
    implementation 'org.telegram:passport:1.1'
}
